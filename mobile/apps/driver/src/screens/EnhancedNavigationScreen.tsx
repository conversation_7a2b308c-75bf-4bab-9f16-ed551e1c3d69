import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator, Dimensions, ScrollView, Switch } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import navigationService, { Coordinates, Route, RouteStep, Maneuver, NavigationConfig } from '../../../../shared/services/navigationService';
import TurnByTurnInstruction from '../components/TurnByTurnInstruction';
import LaneGuidance from '../components/LaneGuidance';

const { width, height } = Dimensions.get('window');

// Default vehicle profile
const defaultVehicleProfile: NavigationConfig = {
  vehicleType: 'truck',
  dimensions: {
    height: 13.5, // feet
    width: 8.5, // feet
    length: 33, // feet
    weight: 26000 // pounds
  },
  routingPreferences: {
    avoidTolls: false,
    avoidHighways: false,
    avoidFerries: true,
    preferFuelEfficient: true
  }
};

const EnhancedNavigationScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const mapRef = useRef<MapView>(null);
  
  // Get delivery ID from route params
  const { deliveryId } = route.params as { deliveryId: string };

  const [loading, setLoading] = useState(true);
  const [initializing, setInitializing] = useState(true);
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [destination, setDestination] = useState<Coordinates | null>(null);
  const [routes, setRoutes] = useState<Route[]>([]);
  const [selectedRoute, setSelectedRoute] = useState<Route | null>(null);
  const [navigationActive, setNavigationActive] = useState(false);
  const [navigationState, setNavigationState] = useState<any>(null);
  const [showInstructions, setShowInstructions] = useState(true);
  const [distanceUnit, setDistanceUnit] = useState<'miles' | 'kilometers'>('miles');
  const [voiceGuidance, setVoiceGuidance] = useState(true);
  const [vehicleProfile, setVehicleProfile] = useState<NavigationConfig>(defaultVehicleProfile);

  // Mock delivery data - in a real app, this would come from an API
  const mockDelivery = {
    id: 'del-001',
    customerName: 'Green Valley Farm',
    address: '1234 Farm Road, Greenville, CA',
    coordinates: {
      latitude: 37.7749,
      longitude: -122.4194,
    }
  };

  // Initialize navigation service and request permissions
  useEffect(() => {
    const initializeNavigation = async () => {
      try {
        // Initialize navigation service with vehicle profile
        await navigationService.initialize(vehicleProfile);
        
        // Request location permissions
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Denied', 'Location permission is required for navigation.');
          setLoading(false);
          return;
        }

        // Get current location
        let location = await Location.getCurrentPositionAsync({});
        setCurrentLocation(location);

        // Set destination from delivery
        setDestination(mockDelivery.coordinates);

        // Get routes
        const routeOptions = {
          alternatives: true,
          optimizeFor: vehicleProfile.routingPreferences.preferFuelEfficient ? 'fuelEfficiency' : 'time'
        };
        
        const availableRoutes = await navigationService.getRoutes(
          { latitude: location.coords.latitude, longitude: location.coords.longitude },
          mockDelivery.coordinates,
          routeOptions
        );
        
        setRoutes(availableRoutes);
        
        // Select the first route by default
        if (availableRoutes.length > 0) {
          setSelectedRoute(availableRoutes[0]);
        }

        setInitializing(false);
        setLoading(false);
      } catch (error) {
        console.error('Error initializing navigation:', error);
        Alert.alert('Error', 'Could not initialize navigation. Please try again.');
        setLoading(false);
      }
    };

    initializeNavigation();

    // Cleanup on unmount
    return () => {
      if (navigationActive) {
        navigationService.endNavigation();
      }
    };
  }, []);

  // Update navigation state when active
  useEffect(() => {
    if (!navigationActive || !currentLocation) return;

    const updateInterval = setInterval(() => {
      // Update navigation with current location
      navigationService.updateNavigation({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude
      });

      // Get updated navigation state
      const state = navigationService.getNavigationState();
      setNavigationState(state);

      // Update current location (in a real app, this would use continuous location tracking)
      Location.getCurrentPositionAsync({}).then(location => {
        setCurrentLocation(location);
      });
    }, 5000); // Update every 5 seconds

    return () => {
      clearInterval(updateInterval);
    };
  }, [navigationActive, currentLocation]);

  // Start navigation with selected route
  const startNavigation = () => {
    if (!selectedRoute) {
      Alert.alert('Error', 'No route selected. Please select a route first.');
      return;
    }

    const success = navigationService.startNavigation(selectedRoute.id);
    if (!success) {
      Alert.alert('Error', 'Failed to start navigation. Please try again.');
      return;
    }

    setNavigationActive(true);
    setNavigationState(navigationService.getNavigationState());

    // Fit map to show route
    if (mapRef.current && currentLocation && destination) {
      mapRef.current.fitToCoordinates(
        [
          { 
            latitude: currentLocation.coords.latitude, 
            longitude: currentLocation.coords.longitude 
          },
          destination
        ],
        {
          edgePadding: { top: 100, right: 50, bottom: 300, left: 50 },
          animated: true,
        }
      );
    }
  };

  // End navigation
  const endNavigation = () => {
    navigationService.endNavigation();
    setNavigationActive(false);
    setNavigationState(null);
  };

  // Select a different route
  const selectRoute = (route: Route) => {
    setSelectedRoute(route);
  };

  // Toggle voice guidance
  const toggleVoiceGuidance = (enabled: boolean) => {
    setVoiceGuidance(enabled);
    navigationService.toggleVoiceGuidance(enabled);
  };

  // Toggle distance unit
  const toggleDistanceUnit = () => {
    const newUnit = distanceUnit === 'miles' ? 'kilometers' : 'miles';
    setDistanceUnit(newUnit);
    navigationService.setDistanceUnit(newUnit);
  };

  // Format time for display
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours} hr ${minutes} min`;
    } else {
      return `${minutes} min`;
    }
  };

  // Format distance for display
  const formatDistance = (meters: number): string => {
    if (distanceUnit === 'miles') {
      const miles = meters / 1609.34;
      return `${miles.toFixed(1)} mi`;
    } else {
      const kilometers = meters / 1000;
      return `${kilometers.toFixed(1)} km`;
    }
  };

  // Render loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>
          {initializing ? 'Initializing navigation...' : 'Loading routes...'}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Map View */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={currentLocation ? {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        } : undefined}
        showsUserLocation
        followsUserLocation
        showsMyLocationButton
        showsCompass
        showsScale
      >
        {/* Destination Marker */}
        {destination && (
          <Marker
            coordinate={destination}
            title={mockDelivery.customerName}
            description={mockDelivery.address}
            pinColor="#8b5cf6"
          />
        )}

        {/* Route Polyline */}
        {selectedRoute && (
          <Polyline
            coordinates={selectedRoute.polyline}
            strokeWidth={4}
            strokeColor="#8b5cf6"
          />
        )}
      </MapView>

      {/* Navigation Instructions Panel */}
      {navigationActive && navigationState && (
        <View style={styles.navigationPanel}>
          <View style={styles.navigationHeader}>
            <Text style={styles.navigationTitle}>
              {navigationState.currentManeuver.instruction}
            </Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={endNavigation}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Current Maneuver */}
          <View style={styles.currentManeuverContainer}>
            <View style={styles.distanceToManeuver}>
              <Text style={styles.distanceText}>
                {formatDistance(navigationState.currentManeuver.distance)}
              </Text>
            </View>
            <View style={styles.maneuverStreet}>
              <Text style={styles.streetText}>
                {navigationState.currentManeuver.street}
              </Text>
            </View>
          </View>

          {/* Lane Guidance */}
          {navigationState.route.steps[navigationState.currentStep].lanes && (
            <LaneGuidance lanes={navigationState.route.steps[navigationState.currentStep].lanes} />
          )}

          {/* Toggle Instructions Button */}
          <TouchableOpacity 
            style={styles.toggleInstructionsButton}
            onPress={() => setShowInstructions(!showInstructions)}
          >
            <Text style={styles.toggleButtonText}>
              {showInstructions ? 'Hide Instructions' : 'Show Instructions'}
            </Text>
            <Ionicons 
              name={showInstructions ? 'chevron-down' : 'chevron-up'} 
              size={20} 
              color="#8b5cf6" 
            />
          </TouchableOpacity>

          {/* Turn-by-Turn Instructions */}
          {showInstructions && (
            <ScrollView style={styles.instructionsScrollView}>
              {navigationState.route.steps.slice(navigationState.currentStep).map((step: RouteStep, index: number) => (
                <TurnByTurnInstruction 
                  key={index}
                  maneuver={step.maneuver}
                  distanceUnit={distanceUnit}
                  isNext={index === 0}
                />
              ))}
            </ScrollView>
          )}

          {/* Navigation Info */}
          <View style={styles.navigationInfo}>
            <View style={styles.infoItem}>
              <Ionicons name="time-outline" size={20} color="#8b5cf6" />
              <Text style={styles.infoText}>
                ETA: {navigationState.eta.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="map-outline" size={20} color="#8b5cf6" />
              <Text style={styles.infoText}>
                Remaining: {formatDistance(navigationState.remainingDistance)}
              </Text>
            </View>
          </View>

          {/* Navigation Settings */}
          <View style={styles.navigationSettings}>
            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Voice Guidance</Text>
              <Switch
                value={voiceGuidance}
                onValueChange={toggleVoiceGuidance}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={voiceGuidance ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>
                {distanceUnit === 'miles' ? 'Miles' : 'Kilometers'}
              </Text>
              <TouchableOpacity onPress={toggleDistanceUnit}>
                <Text style={styles.unitToggleText}>
                  Switch to {distanceUnit === 'miles' ? 'km' : 'mi'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Route Selection Panel */}
      {!navigationActive && routes.length > 0 && (
        <View style={styles.routeSelectionPanel}>
          <View style={styles.panelHeader}>
            <Text style={styles.panelTitle}>Select Route</Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <Text style={styles.destinationText}>
            To: {mockDelivery.customerName}
          </Text>
          <Text style={styles.addressText}>
            {mockDelivery.address}
          </Text>

          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.routesScrollView}
            contentContainerStyle={styles.routesContainer}
          >
            {routes.map((route, index) => (
              <TouchableOpacity
                key={route.id}
                style={[
                  styles.routeOption,
                  selectedRoute?.id === route.id && styles.selectedRouteOption
                ]}
                onPress={() => selectRoute(route)}
              >
                <Text style={styles.routeOptionTitle}>
                  Route {index + 1}
                </Text>
                <View style={styles.routeOptionDetails}>
                  <View style={styles.routeDetailItem}>
                    <Ionicons name="time-outline" size={16} color="#6b7280" />
                    <Text style={styles.routeDetailText}>
                      {formatTime(route.summary.duration)}
                    </Text>
                  </View>
                  <View style={styles.routeDetailItem}>
                    <Ionicons name="map-outline" size={16} color="#6b7280" />
                    <Text style={styles.routeDetailText}>
                      {formatDistance(route.summary.distance)}
                    </Text>
                  </View>
                  {route.summary.fuelUsage && (
                    <View style={styles.routeDetailItem}>
                      <Ionicons name="car-outline" size={16} color="#6b7280" />
                      <Text style={styles.routeDetailText}>
                        {route.summary.fuelUsage.toFixed(1)} gal
                      </Text>
                    </View>
                  )}
                  {route.summary.tolls !== undefined && route.summary.tolls > 0 && (
                    <View style={styles.routeDetailItem}>
                      <Ionicons name="cash-outline" size={16} color="#6b7280" />
                      <Text style={styles.routeDetailText}>
                        ${route.summary.tolls.toFixed(2)}
                      </Text>
                    </View>
                  )}
                </View>
                {index === 0 && (
                  <View style={styles.recommendedBadge}>
                    <Text style={styles.recommendedText}>Recommended</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          <TouchableOpacity 
            style={styles.startNavigationButton}
            onPress={startNavigation}
          >
            <Ionicons name="navigate" size={20} color="white" />
            <Text style={styles.startNavigationText}>Start Navigation</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.vehicleProfileButton}
            onPress={() => navigation.navigate('VehicleProfile' as never)}
          >
            <Ionicons name="car" size={16} color="#8b5cf6" />
            <Text style={styles.vehicleProfileText}>
              Vehicle Profile: {vehicleProfile.vehicleType}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  map: {
    width,
    height,
  },
  navigationPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: height * 0.7,
  },
  navigationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  navigationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    flex: 1,
  },
  closeButton: {
    padding: 5,
  },
  currentManeuverContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  distanceToManeuver: {
    backgroundColor: '#f3f0ff',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 10,
  },
  distanceText: {
    color: '#8b5cf6',
    fontWeight: '600',
    fontSize: 16,
  },
  maneuverStreet: {
    flex: 1,
  },
  streetText: {
    fontSize: 16,
    color: '#4b5563',
  },
  toggleInstructionsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    marginVertical: 10,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#f3f4f6',
  },
  toggleButtonText: {
    color: '#8b5cf6',
    fontWeight: '500',
    marginRight: 5,
  },
  instructionsScrollView: {
    maxHeight: 200,
  },
  navigationInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderColor: '#f3f4f6',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    color: '#4b5563',
    marginLeft: 5,
    fontWeight: '500',
  },
  navigationSettings: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 15,
    borderTopWidth: 1,
    borderColor: '#f3f4f6',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingLabel: {
    fontSize: 14,
    color: '#4b5563',
    marginRight: 10,
  },
  unitToggleText: {
    color: '#8b5cf6',
    fontWeight: '500',
  },
  routeSelectionPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  panelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  panelTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  destinationText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  addressText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 15,
  },
  routesScrollView: {
    marginBottom: 15,
  },
  routesContainer: {
    paddingRight: 20,
  },
  routeOption: {
    width: 200,
    backgroundColor: '#f9fafb',
    borderRadius: 10,
    padding: 15,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  selectedRouteOption: {
    borderColor: '#8b5cf6',
    backgroundColor: '#f3f0ff',
  },
  routeOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 10,
  },
  routeOptionDetails: {
    gap: 5,
  },
  routeDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeDetailText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 5,
  },
  recommendedBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#8b5cf6',
    paddingVertical: 3,
    paddingHorizontal: 8,
    borderRadius: 10,
  },
  recommendedText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  startNavigationButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 10,
  },
  startNavigationText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  vehicleProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  vehicleProfileText: {
    color: '#8b5cf6',
    marginLeft: 5,
  },
});

export default EnhancedNavigationScreen;