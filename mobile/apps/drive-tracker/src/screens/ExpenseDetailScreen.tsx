import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Alert,
  ActivityIndicator,
  Share
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ExpenseDetailScreenNavigationProp, ExpenseDetailScreenRouteProp } from '../types/navigation';
import { getExpenseById, deleteExpense, Expense } from '@qbooks/shared/services/expenseService';

// Helper function to determine confidence level color
const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.9) {
    return { backgroundColor: '#4CAF50' }; // High confidence - Green
  } else if (confidence >= 0.7) {
    return { backgroundColor: '#FFC107' }; // Medium confidence - Yellow
  } else {
    return { backgroundColor: '#FF5252' }; // Low confidence - Red
  }
};

const ExpenseDetailScreen = () => {
  const navigation = useNavigation<ExpenseDetailScreenNavigationProp>();
  const route = useRoute<ExpenseDetailScreenRouteProp>();
  const { expenseId } = route.params;

  const [expense, setExpense] = useState<Expense | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch expense data
  useEffect(() => {
    const fetchExpense = async () => {
      try {
        setLoading(true);
        const fetchedExpense = await getExpenseById(expenseId);

        if (fetchedExpense) {
          setExpense(fetchedExpense);
        } else {
          setError('Expense not found');
        }
        setLoading(false);
      } catch (err) {
        console.error('Error fetching expense:', err);
        setError('Failed to load expense details');
        setLoading(false);
      }
    };

    fetchExpense();
  }, [expenseId]);

  // Handle edit expense
  const handleEdit = () => {
    // In a real app, this would navigate to the edit screen with the expense data
    navigation.navigate('AddExpense', { expenseId: expense.id });
  };

  // Handle delete expense
  const handleDelete = () => {
    Alert.alert(
      'Delete Expense',
      'Are you sure you want to delete this expense? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            try {
              setLoading(true);
              const success = await deleteExpense(expense.id);

              if (success) {
                Alert.alert('Success', 'Expense has been deleted.');
                navigation.goBack();
              } else {
                throw new Error('Failed to delete expense');
              }
            } catch (error) {
              console.error('Error deleting expense:', error);
              Alert.alert('Error', 'Failed to delete expense. Please try again.');
              setLoading(false);
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  // Handle share expense
  const handleShare = async () => {
    try {
      await Share.share({
        message: `Expense: ${expense.description}\nAmount: $${expense.amount.toFixed(2)}\nDate: ${expense.date}\nCategory: ${expense.category}`,
        title: 'Expense Details',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share expense details');
    }
  };

  // Handle view receipt
  const handleViewReceipt = () => {
    // In a real app, this would open a full-screen view of the receipt
    Alert.alert('View Receipt', 'This would open a full-screen view of the receipt');
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading expense details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF5252" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => navigation.goBack()}>
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!expense) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF5252" />
        <Text style={styles.errorText}>Expense not found</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => navigation.goBack()}>
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.amountContainer}>
            <Text style={styles.currencySymbol}>$</Text>
            <Text style={styles.amount}>{expense.amount.toFixed(2)}</Text>
          </View>
          <Text style={styles.date}>{new Date(expense.date).toLocaleDateString()}</Text>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>{expense.category}</Text>
          </View>
        </View>
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
          <Ionicons name="create-outline" size={20} color="#4CAF50" />
          <Text style={styles.actionButtonText}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Ionicons name="share-outline" size={20} color="#2196F3" />
          <Text style={styles.actionButtonText}>Share</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
          <Ionicons name="trash-outline" size={20} color="#FF5252" />
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.detailSection}>
        <Text style={styles.sectionTitle}>Details</Text>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Description</Text>
          <Text style={styles.detailValue}>{expense.description}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Vehicle</Text>
          <Text style={styles.detailValue}>{expense.vehicle.name}</Text>
        </View>

        {expense.trip && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Trip</Text>
            <View>
              <Text style={styles.detailValue}>{expense.trip.startLocation} → {expense.trip.endLocation}</Text>
              <Text style={styles.detailSubvalue}>{expense.trip.date} • {expense.trip.distance} miles • {expense.trip.category}</Text>
            </View>
          </View>
        )}

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Tax Deductible</Text>
          <View style={styles.taxDeductibleIndicator}>
            <Ionicons 
              name={expense.isTaxDeductible ? "checkmark-circle" : "close-circle"} 
              size={20} 
              color={expense.isTaxDeductible ? "#4CAF50" : "#FF5252"} 
            />
            <Text 
              style={[
                styles.taxDeductibleText, 
                { color: expense.isTaxDeductible ? "#4CAF50" : "#FF5252" }
              ]}
            >
              {expense.isTaxDeductible ? "Yes" : "No"}
            </Text>
          </View>
        </View>

        {expense.notes && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Notes</Text>
            <Text style={styles.detailValue}>{expense.notes}</Text>
          </View>
        )}
      </View>

      {expense.receiptImage && (
        <View style={styles.receiptSection}>
          <Text style={styles.sectionTitle}>Receipt</Text>

          <TouchableOpacity onPress={handleViewReceipt}>
            <Image 
              source={{ uri: expense.receiptImage }} 
              style={styles.receiptImage}
              resizeMode="cover"
            />
            <View style={styles.viewReceiptButton}>
              <Ionicons name="eye-outline" size={16} color="#FFFFFF" />
              <Text style={styles.viewReceiptText}>View Full Receipt</Text>
            </View>
          </TouchableOpacity>
        </View>
      )}

      {expense.aiProcessed && (
        <View style={styles.aiSection}>
          <Text style={styles.sectionTitle}>AI Analysis</Text>

          <View style={styles.aiInfoContainer}>
            <Ionicons name="analytics-outline" size={20} color="#4CAF50" />
            <Text style={styles.aiInfoText}>
              This expense was automatically categorized using AI
            </Text>
          </View>

          <Text style={styles.confidenceTitle}>Confidence Levels</Text>

          <View style={styles.confidenceRow}>
            <Text style={styles.confidenceLabel}>Amount</Text>
            <View style={styles.confidenceBarContainer}>
              <View 
                style={[
                  styles.confidenceBar, 
                  { width: `${expense.confidenceLevels.amount * 100}%` },
                  getConfidenceColor(expense.confidenceLevels.amount)
                ]} 
              />
            </View>
            <Text style={styles.confidencePercentage}>
              {Math.round(expense.confidenceLevels.amount * 100)}%
            </Text>
          </View>

          <View style={styles.confidenceRow}>
            <Text style={styles.confidenceLabel}>Date</Text>
            <View style={styles.confidenceBarContainer}>
              <View 
                style={[
                  styles.confidenceBar, 
                  { width: `${expense.confidenceLevels.date * 100}%` },
                  getConfidenceColor(expense.confidenceLevels.date)
                ]} 
              />
            </View>
            <Text style={styles.confidencePercentage}>
              {Math.round(expense.confidenceLevels.date * 100)}%
            </Text>
          </View>

          <View style={styles.confidenceRow}>
            <Text style={styles.confidenceLabel}>Category</Text>
            <View style={styles.confidenceBarContainer}>
              <View 
                style={[
                  styles.confidenceBar, 
                  { width: `${expense.confidenceLevels.category * 100}%` },
                  getConfidenceColor(expense.confidenceLevels.category)
                ]} 
              />
            </View>
            <Text style={styles.confidencePercentage}>
              {Math.round(expense.confidenceLevels.category * 100)}%
            </Text>
          </View>

          <View style={styles.confidenceRow}>
            <Text style={styles.confidenceLabel}>Merchant</Text>
            <View style={styles.confidenceBarContainer}>
              <View 
                style={[
                  styles.confidenceBar, 
                  { width: `${expense.confidenceLevels.merchant * 100}%` },
                  getConfidenceColor(expense.confidenceLevels.merchant)
                ]} 
              />
            </View>
            <Text style={styles.confidencePercentage}>
              {Math.round(expense.confidenceLevels.merchant * 100)}%
            </Text>
          </View>

          {expense.alternativeCategories && expense.alternativeCategories.length > 0 && (
            <>
              <Text style={styles.alternativesTitle}>Alternative Categories</Text>
              <View style={styles.alternativesContainer}>
                {expense.alternativeCategories.map((category, index) => (
                  <TouchableOpacity 
                    key={index} 
                    style={styles.alternativeChip}
                    onPress={() => Alert.alert('Change Category', `Would change category to ${category}`)}
                  >
                    <Text style={styles.alternativeChipText}>{category}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </>
          )}

          <TouchableOpacity 
            style={styles.editAiButton}
            onPress={() => Alert.alert('Edit AI Results', 'Would open a screen to edit AI-extracted data')}
          >
            <Ionicons name="create-outline" size={16} color="#FFFFFF" />
            <Text style={styles.editAiButtonText}>Edit AI Results</Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.metadataSection}>
        <Text style={styles.metadataText}>Created: {new Date(expense.createdAt).toLocaleString()}</Text>
        {expense.updatedAt !== expense.createdAt && (
          <Text style={styles.metadataText}>Last Updated: {new Date(expense.updatedAt).toLocaleString()}</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: '#4CAF50',
    padding: 20,
    paddingBottom: 30,
  },
  headerContent: {
    alignItems: 'center',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  currencySymbol: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 5,
  },
  amount: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  date: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  categoryBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 20,
    marginTop: 10,
  },
  categoryText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginTop: -15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButton: {
    alignItems: 'center',
  },
  actionButtonText: {
    marginTop: 5,
    fontSize: 12,
    color: '#757575',
  },
  detailSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginBottom: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#212121',
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 15,
    alignItems: 'flex-start',
  },
  detailLabel: {
    width: 120,
    fontSize: 16,
    color: '#757575',
  },
  detailValue: {
    flex: 1,
    fontSize: 16,
    color: '#212121',
  },
  detailSubvalue: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  taxDeductibleIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taxDeductibleText: {
    fontSize: 16,
    marginLeft: 5,
  },
  receiptSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginBottom: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  receiptImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  viewReceiptButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewReceiptText: {
    color: '#FFFFFF',
    marginLeft: 5,
    fontSize: 12,
  },
  metadataSection: {
    margin: 15,
    marginTop: 5,
    marginBottom: 30,
  },
  metadataText: {
    fontSize: 12,
    color: '#9E9E9E',
    textAlign: 'center',
  },
  // AI Analysis section styles
  aiSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginBottom: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  aiInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
  },
  aiInfoText: {
    marginLeft: 10,
    color: '#4CAF50',
    fontSize: 14,
  },
  confidenceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#424242',
  },
  confidenceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  confidenceLabel: {
    width: 80,
    fontSize: 14,
    color: '#757575',
  },
  confidenceBarContainer: {
    flex: 1,
    height: 10,
    backgroundColor: '#E0E0E0',
    borderRadius: 5,
    marginHorizontal: 10,
    overflow: 'hidden',
  },
  confidenceBar: {
    height: '100%',
    borderRadius: 5,
  },
  confidencePercentage: {
    width: 40,
    fontSize: 14,
    color: '#757575',
    textAlign: 'right',
  },
  alternativesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 10,
    color: '#424242',
  },
  alternativesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 15,
  },
  alternativeChip: {
    backgroundColor: '#E0E0E0',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 4,
  },
  alternativeChipText: {
    color: '#424242',
    fontSize: 14,
  },
  editAiButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
  editAiButtonText: {
    color: '#FFFFFF',
    marginLeft: 5,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ExpenseDetailScreen;
