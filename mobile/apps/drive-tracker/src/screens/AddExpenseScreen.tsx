import React, { useState, useEffect, useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TextInput, 
  TouchableOpacity, 
  Switch,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AddExpenseScreenNavigationProp } from '../types/navigation';
import * as ImagePicker from 'expo-image-picker';
import { debounce } from 'lodash';

// Mock data for dropdowns
const mockVehicles = [
  { id: '1', name: 'My Car', make: 'Toyota', model: 'Camry', year: 2020 },
  { id: '2', name: 'Work Truck', make: 'Ford', model: 'F-150', year: 2019 },
];

const mockTrips = [
  { id: '1', date: '2023-06-20', startLocation: 'Home', endLocation: 'Office', distance: 12.5 },
  { id: '2', date: '2023-06-19', startLocation: 'Office', endLocation: 'Client Meeting', distance: 8.3 },
  { id: '3', date: '2023-06-18', startLocation: 'Home', endLocation: 'Grocery Store', distance: 3.7 },
];

const mockCategories = [
  'Fuel', 'Maintenance', 'Parking', 'Tolls', 'Insurance', 'Registration', 'Other'
];

// Keyword mappings for AI-based categorization
const categoryKeywords = {
  'Fuel': ['gas', 'fuel', 'unleaded', 'diesel', 'pump', 'gallon', 'shell', 'exxon', 'mobil', 'chevron', 'arco', 'bp', 'petrol', 'fill', 'tank'],
  'Maintenance': ['oil', 'change', 'filter', 'maintenance', 'service', 'repair', 'tire', 'wheel', 'alignment', 'brake', 'battery', 'wiper', 'bulb', 'light', 'jiffy', 'midas', 'autozone', 'mechanic', 'parts', 'fluid', 'coolant', 'transmission', 'engine', 'diagnostic', 'inspection'],
  'Parking': ['parking', 'garage', 'lot', 'meter', 'space', 'valet', 'airport', 'downtown', 'city', 'fee', 'spot'],
  'Tolls': ['toll', 'highway', 'bridge', 'pass', 'road', 'turnpike', 'ezpass', 'fastrak', 'express', 'lane', 'transponder'],
  'Insurance': ['insurance', 'premium', 'coverage', 'policy', 'liability', 'comprehensive', 'collision', 'deductible', 'claim', 'geico', 'allstate', 'state farm', 'progressive', 'nationwide'],
  'Registration': ['registration', 'license', 'dmv', 'renewal', 'annual', 'plate', 'tag', 'sticker', 'vehicle', 'department', 'motor'],
  'Other': []
};

// User feedback data store for learning
// In a real app, this would be stored in a database or local storage
const userFeedbackStore = {
  corrections: [],
  addCorrection: (originalSuggestion, userCorrection, description) => {
    userFeedbackStore.corrections.push({
      originalSuggestion,
      userCorrection,
      description,
      timestamp: new Date().toISOString()
    });
    // In a real app, this would be persisted to storage
    console.log('Added correction to learning system:', { originalSuggestion, userCorrection, description });
  }
};

const AddExpenseScreen = () => {
  const navigation = useNavigation<AddExpenseScreenNavigationProp>();

  // Form state
  const [expenseData, setExpenseData] = useState({
    amount: '',
    date: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
    category: '',
    description: '',
    vehicleId: mockVehicles[0].id,
    tripId: '',
    isTaxDeductible: true,
    receiptImage: null,
    notes: '',
  });

  // UI state
  const [showVehicleDropdown, setShowVehicleDropdown] = useState(false);
  const [showTripDropdown, setShowTripDropdown] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState(mockVehicles[0]);
  const [selectedTrip, setSelectedTrip] = useState(null);

  // OCR state
  const [isProcessingOcr, setIsProcessingOcr] = useState(false);
  const [ocrResults, setOcrResults] = useState(null);
  const [showOcrModal, setShowOcrModal] = useState(false);

  // AI suggestion state
  const [aiSuggestions, setAiSuggestions] = useState(null);
  const [isProcessingAi, setIsProcessingAi] = useState(false);
  const [showAiSuggestions, setShowAiSuggestions] = useState(false);

  // Effect to automatically process receipt image with OCR when uploaded
  useEffect(() => {
    if (expenseData.receiptImage && !ocrResults && !isProcessingOcr) {
      // Add a small delay to allow the UI to update first
      const timer = setTimeout(() => {
        processReceiptWithOcr(expenseData.receiptImage);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [expenseData.receiptImage]);

  // Validation state
  const [errors, setErrors] = useState({
    amount: '',
    date: '',
    category: '',
    description: '',
    vehicleId: '',
  });

  // Analyze description and suggest categories
  const analyzeDescriptionForCategory = (description) => {
    if (!description || description.trim() === '') {
      setAiSuggestions(null);
      setShowAiSuggestions(false);
      return;
    }

    setIsProcessingAi(true);

    // In a real app, this would call an AI service
    // For now, we'll simulate AI processing with keyword matching
    setTimeout(() => {
      const descriptionLower = description.toLowerCase();
      const matches = {};

      // Check for matches in each category
      Object.keys(categoryKeywords).forEach(category => {
        const keywords = categoryKeywords[category];
        let matchCount = 0;
        let matchedKeywords = [];

        keywords.forEach(keyword => {
          if (descriptionLower.includes(keyword.toLowerCase())) {
            matchCount++;
            matchedKeywords.push(keyword);
          }
        });

        if (matchCount > 0) {
          // Calculate confidence based on number of matches and keyword relevance
          const confidence = Math.min(0.5 + (matchCount * 0.1), 0.95);
          matches[category] = {
            confidence,
            matchCount,
            matchedKeywords
          };
        }
      });

      // Check user feedback store for learning
      userFeedbackStore.corrections.forEach(correction => {
        if (descriptionLower.includes(correction.description.toLowerCase())) {
          // Boost confidence for categories that users have corrected to in similar descriptions
          if (matches[correction.userCorrection]) {
            matches[correction.userCorrection].confidence = 
              Math.min(matches[correction.userCorrection].confidence + 0.1, 0.98);
          } else {
            matches[correction.userCorrection] = {
              confidence: 0.7, // Start with decent confidence for learned categories
              matchCount: 1,
              matchedKeywords: ['learned from user feedback']
            };
          }
        }
      });

      // If no matches found, suggest "Other" with low confidence
      if (Object.keys(matches).length === 0) {
        matches['Other'] = {
          confidence: 0.3,
          matchCount: 0,
          matchedKeywords: []
        };
      }

      // Sort categories by confidence
      const sortedCategories = Object.keys(matches)
        .sort((a, b) => matches[b].confidence - matches[a].confidence);

      // Prepare suggestions with primary and alternatives
      const suggestions = {
        primaryCategory: sortedCategories[0],
        primaryConfidence: matches[sortedCategories[0]].confidence,
        alternativeCategories: sortedCategories.slice(1, 4),
        confidenceLevels: sortedCategories.reduce((acc, category) => {
          acc[category] = matches[category].confidence;
          return acc;
        }, {}),
        matchDetails: matches
      };

      setAiSuggestions(suggestions);
      setShowAiSuggestions(true);
      setIsProcessingAi(false);
    }, 500); // Simulate processing delay
  };

  // Debounced version of analyzeDescriptionForCategory
  const debouncedAnalyzeDescription = useCallback(
    debounce((description) => {
      analyzeDescriptionForCategory(description);
    }, 800),
    []
  );

  // Handle text input changes
  const handleChange = (field, value) => {
    setExpenseData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user types
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }

    // Trigger AI category suggestions when description changes
    if (field === 'description' && value.trim().length > 2) {
      debouncedAnalyzeDescription(value);
    }
  };

  // Apply AI category suggestion
  const applyAiCategorySuggestion = (category) => {
    // If user selects a different category than the primary suggestion,
    // add this correction to the learning system
    if (aiSuggestions && category !== aiSuggestions.primaryCategory) {
      userFeedbackStore.addCorrection(
        aiSuggestions.primaryCategory,
        category,
        expenseData.description
      );
    }

    setExpenseData(prev => ({
      ...prev,
      category
    }));
    setShowAiSuggestions(false);
  };

  // Toggle tax deductible
  const toggleTaxDeductible = () => {
    setExpenseData(prev => ({
      ...prev,
      isTaxDeductible: !prev.isTaxDeductible
    }));
  };

  // Select vehicle
  const selectVehicle = (vehicle) => {
    setSelectedVehicle(vehicle);
    setExpenseData(prev => ({
      ...prev,
      vehicleId: vehicle.id
    }));
    setShowVehicleDropdown(false);
  };

  // Select trip
  const selectTrip = (trip) => {
    setSelectedTrip(trip);
    setExpenseData(prev => ({
      ...prev,
      tripId: trip.id
    }));
    setShowTripDropdown(false);
  };

  // Select category
  const selectCategory = (category) => {
    setExpenseData(prev => ({
      ...prev,
      category
    }));
    setShowCategoryDropdown(false);
  };

  // Pick receipt image
  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to upload a receipt.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      setExpenseData(prev => ({
        ...prev,
        receiptImage: result.assets[0].uri
      }));
    }
  };

  // Take photo of receipt
  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera permissions to take a photo.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      setExpenseData(prev => ({
        ...prev,
        receiptImage: result.assets[0].uri
      }));
    }
  };

  // Remove receipt image
  const removeImage = () => {
    setExpenseData(prev => ({
      ...prev,
      receiptImage: null
    }));
    setOcrResults(null);
  };

  // Process receipt image with OCR
  const processReceiptWithOcr = async (imageUri) => {
    setIsProcessingOcr(true);

    try {
      // In a real implementation, this would call an OCR API service
      // For now, we'll simulate OCR processing with a timeout and mock data
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock OCR results based on different receipt types
      // In a real app, this would be the result from the OCR API
      const mockOcrResults = generateMockOcrResults();

      setOcrResults(mockOcrResults);
      setShowOcrModal(true);
    } catch (error) {
      Alert.alert('OCR Error', 'Failed to process receipt. Please try again or enter details manually.');
      console.error('OCR processing error:', error);
    } finally {
      setIsProcessingOcr(false);
    }
  };

  // Generate mock OCR results with AI-assisted categorization
  const generateMockOcrResults = () => {
    // Expanded list of mock receipt types to simulate more diverse data
    const receiptTypes = [
      {
        merchantName: 'Shell Gas Station',
        amount: '45.67',
        date: new Date().toISOString().split('T')[0],
        category: 'Fuel',
        description: 'Regular unleaded gas',
        keywords: ['gas', 'fuel', 'unleaded', 'shell', 'pump', 'gallon'],
      },
      {
        merchantName: 'Jiffy Lube',
        amount: '89.99',
        date: new Date().toISOString().split('T')[0],
        category: 'Maintenance',
        description: 'Oil change and filter',
        keywords: ['oil', 'change', 'filter', 'maintenance', 'service', 'lube'],
      },
      {
        merchantName: 'City Parking',
        amount: '15.00',
        date: new Date().toISOString().split('T')[0],
        category: 'Parking',
        description: 'Downtown parking fee',
        keywords: ['parking', 'garage', 'lot', 'fee', 'meter', 'space'],
      },
      {
        merchantName: 'EZ Pass',
        amount: '8.50',
        date: new Date().toISOString().split('T')[0],
        category: 'Tolls',
        description: 'Highway toll',
        keywords: ['toll', 'highway', 'bridge', 'pass', 'road', 'turnpike'],
      },
      {
        merchantName: 'AutoZone',
        amount: '32.99',
        date: new Date().toISOString().split('T')[0],
        category: 'Maintenance',
        description: 'Wiper blades and air filter',
        keywords: ['parts', 'auto', 'filter', 'wiper', 'blades', 'accessories'],
      },
      {
        merchantName: 'Discount Tire',
        amount: '450.00',
        date: new Date().toISOString().split('T')[0],
        category: 'Maintenance',
        description: 'New tires (2)',
        keywords: ['tire', 'wheel', 'rotation', 'balance', 'alignment', 'rubber'],
      },
      {
        merchantName: 'ARCO Gas',
        amount: '42.35',
        date: new Date().toISOString().split('T')[0],
        category: 'Fuel',
        description: 'Premium fuel',
        keywords: ['gas', 'fuel', 'premium', 'arco', 'pump', 'gallon'],
      },
      {
        merchantName: 'DMV',
        amount: '120.00',
        date: new Date().toISOString().split('T')[0],
        category: 'Registration',
        description: 'Annual vehicle registration',
        keywords: ['registration', 'license', 'dmv', 'renewal', 'annual', 'fee'],
      }
    ];

    // Randomly select a receipt type
    const selectedReceipt = receiptTypes[Math.floor(Math.random() * receiptTypes.length)];

    // Simulate AI confidence levels for each field
    // Higher confidence for structured data like amounts, lower for interpretive data like categories
    const confidenceLevels = {
      merchantName: Math.random() * 0.3 + 0.7, // 70-100% confidence
      amount: Math.random() * 0.2 + 0.8, // 80-100% confidence
      date: Math.random() * 0.2 + 0.8, // 80-100% confidence
      category: Math.random() * 0.4 + 0.6, // 60-100% confidence
      description: Math.random() * 0.5 + 0.5, // 50-100% confidence
    };

    // Generate alternative category suggestions based on keywords
    const allCategories = ['Fuel', 'Maintenance', 'Parking', 'Tolls', 'Insurance', 'Registration', 'Other'];
    const alternativeCategories = allCategories
      .filter(cat => cat !== selectedReceipt.category)
      .sort(() => 0.5 - Math.random())
      .slice(0, 2); // Get 2 random alternative categories

    return {
      ...selectedReceipt,
      confidenceLevels,
      alternativeCategories,
    };
  };

  // State for edited OCR results
  const [editedOcrResults, setEditedOcrResults] = useState(null);

  // Reset edited OCR results when original results change
  useEffect(() => {
    if (ocrResults) {
      setEditedOcrResults({ ...ocrResults });
    }
  }, [ocrResults]);

  // Handle OCR result field edit
  const handleOcrFieldEdit = (field, value) => {
    if (!editedOcrResults) return;

    setEditedOcrResults(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Select alternative category
  const selectAlternativeCategory = (category) => {
    handleOcrFieldEdit('category', category);
  };

  // Apply OCR results to form
  const applyOcrResults = () => {
    if (!editedOcrResults) return;

    setExpenseData(prev => ({
      ...prev,
      amount: editedOcrResults.amount,
      date: editedOcrResults.date,
      category: editedOcrResults.category,
      description: editedOcrResults.description,
    }));

    setShowOcrModal(false);
  };

  // Validate form
  const validateForm = () => {
    let isValid = true;
    const newErrors = { amount: '', date: '', category: '', description: '', vehicleId: '' };

    if (!expenseData.amount.trim()) {
      newErrors.amount = 'Amount is required';
      isValid = false;
    } else if (!/^\d+(\.\d{1,2})?$/.test(expenseData.amount)) {
      newErrors.amount = 'Please enter a valid amount';
      isValid = false;
    }

    if (!expenseData.date.trim()) {
      newErrors.date = 'Date is required';
      isValid = false;
    }

    if (!expenseData.category.trim()) {
      newErrors.category = 'Category is required';
      isValid = false;
    }

    if (!expenseData.description.trim()) {
      newErrors.description = 'Description is required';
      isValid = false;
    }

    if (!expenseData.vehicleId) {
      newErrors.vehicleId = 'Vehicle is required';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      // In a real implementation, this would save the expense to the database
      Alert.alert(
        'Success',
        `Expense of $${expenseData.amount} has been added.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }
  };

  // Handle cancel
  const handleCancel = () => {
    Alert.alert(
      'Discard Changes',
      'Are you sure you want to discard your changes?',
      [
        {
          text: 'Continue Editing',
          style: 'cancel',
        },
        {
          text: 'Discard',
          onPress: () => navigation.goBack(),
          style: 'destructive',
        },
      ]
    );
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView style={styles.container}>
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Expense Details</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Amount*</Text>
            <TextInput
              style={[styles.input, errors.amount ? styles.inputError : null]}
              value={expenseData.amount}
              onChangeText={(text) => handleChange('amount', text)}
              placeholder="0.00"
              keyboardType="decimal-pad"
            />
            {errors.amount ? <Text style={styles.errorText}>{errors.amount}</Text> : null}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Date*</Text>
            <TextInput
              style={[styles.input, errors.date ? styles.inputError : null]}
              value={expenseData.date}
              onChangeText={(text) => handleChange('date', text)}
              placeholder="YYYY-MM-DD"
            />
            {errors.date ? <Text style={styles.errorText}>{errors.date}</Text> : null}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Category*</Text>
            <TouchableOpacity
              style={[styles.dropdownButton, errors.category ? styles.inputError : null]}
              onPress={() => setShowCategoryDropdown(!showCategoryDropdown)}
            >
              <Text style={expenseData.category ? styles.dropdownButtonText : styles.dropdownButtonPlaceholder}>
                {expenseData.category || 'Select a category'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#757575" />
            </TouchableOpacity>
            {errors.category ? <Text style={styles.errorText}>{errors.category}</Text> : null}

            {showCategoryDropdown && (
              <View style={styles.dropdown}>
                {mockCategories.map(category => (
                  <TouchableOpacity
                    key={category}
                    style={styles.dropdownItem}
                    onPress={() => selectCategory(category)}
                  >
                    <Text style={styles.dropdownItemText}>{category}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description*</Text>
            <View>
              <TextInput
                style={[styles.input, errors.description ? styles.inputError : null]}
                value={expenseData.description}
                onChangeText={(text) => handleChange('description', text)}
                placeholder="e.g. Gas fill-up, Oil change"
              />
              {isProcessingAi && (
                <View style={styles.aiProcessingIndicator}>
                  <ActivityIndicator size="small" color="#4CAF50" />
                  <Text style={styles.aiProcessingText}>AI analyzing description...</Text>
                </View>
              )}
              {showAiSuggestions && aiSuggestions && (
                <View style={styles.aiSuggestionsContainer}>
                  <View style={styles.aiSuggestionsHeader}>
                    <Ionicons name="bulb-outline" size={16} color="#4CAF50" />
                    <Text style={styles.aiSuggestionsTitle}>AI Category Suggestions</Text>
                  </View>

                  <View style={styles.aiSuggestionItem}>
                    <TouchableOpacity 
                      style={[styles.categoryChip, styles.categoryChipSelected]}
                      onPress={() => applyAiCategorySuggestion(aiSuggestions.primaryCategory)}
                    >
                      <Text style={styles.categoryChipTextSelected}>{aiSuggestions.primaryCategory}</Text>
                    </TouchableOpacity>
                    <View style={styles.confidenceIndicator}>
                      <View 
                        style={[
                          styles.confidenceBar, 
                          { 
                            width: `${aiSuggestions.primaryConfidence * 100}%`,
                            backgroundColor: aiSuggestions.primaryConfidence > 0.8 ? '#4CAF50' : 
                                            aiSuggestions.primaryConfidence > 0.6 ? '#FFC107' : '#FF5252'
                          }
                        ]} 
                      />
                    </View>
                    <Text style={styles.confidenceText}>
                      {Math.round(aiSuggestions.primaryConfidence * 100)}% confidence
                    </Text>
                  </View>

                  {aiSuggestions.alternativeCategories.length > 0 && (
                    <View style={styles.alternativesContainer}>
                      <Text style={styles.alternativesLabel}>Alternatives:</Text>
                      <View style={styles.alternativeChips}>
                        {aiSuggestions.alternativeCategories.map((category, index) => (
                          <TouchableOpacity 
                            key={index}
                            style={styles.categoryChip}
                            onPress={() => applyAiCategorySuggestion(category)}
                          >
                            <Text style={styles.categoryChipText}>{category}</Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  )}

                  <View style={styles.aiSuggestionsFooter}>
                    <TouchableOpacity 
                      style={styles.applySuggestionButton}
                      onPress={() => applyAiCategorySuggestion(aiSuggestions.primaryCategory)}
                    >
                      <Text style={styles.applySuggestionButtonText}>Apply Suggestion</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={styles.dismissSuggestionButton}
                      onPress={() => setShowAiSuggestions(false)}
                    >
                      <Text style={styles.dismissSuggestionButtonText}>Dismiss</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
            {errors.description ? <Text style={styles.errorText}>{errors.description}</Text> : null}
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Vehicle & Trip</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Vehicle*</Text>
            <TouchableOpacity
              style={[styles.dropdownButton, errors.vehicleId ? styles.inputError : null]}
              onPress={() => setShowVehicleDropdown(!showVehicleDropdown)}
            >
              <Text style={styles.dropdownButtonText}>
                {selectedVehicle ? selectedVehicle.name : 'Select a vehicle'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#757575" />
            </TouchableOpacity>
            {errors.vehicleId ? <Text style={styles.errorText}>{errors.vehicleId}</Text> : null}

            {showVehicleDropdown && (
              <View style={styles.dropdown}>
                {mockVehicles.map(vehicle => (
                  <TouchableOpacity
                    key={vehicle.id}
                    style={styles.dropdownItem}
                    onPress={() => selectVehicle(vehicle)}
                  >
                    <Text style={styles.dropdownItemText}>{vehicle.name}</Text>
                    <Text style={styles.dropdownItemSubtext}>{vehicle.year} {vehicle.make} {vehicle.model}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Associated Trip (Optional)</Text>
            <TouchableOpacity
              style={styles.dropdownButton}
              onPress={() => setShowTripDropdown(!showTripDropdown)}
            >
              <Text style={selectedTrip ? styles.dropdownButtonText : styles.dropdownButtonPlaceholder}>
                {selectedTrip 
                  ? `${selectedTrip.startLocation} → ${selectedTrip.endLocation} (${selectedTrip.date})` 
                  : 'Select a trip (optional)'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#757575" />
            </TouchableOpacity>

            {showTripDropdown && (
              <View style={styles.dropdown}>
                <TouchableOpacity
                  style={styles.dropdownItem}
                  onPress={() => {
                    setSelectedTrip(null);
                    setExpenseData(prev => ({ ...prev, tripId: '' }));
                    setShowTripDropdown(false);
                  }}
                >
                  <Text style={styles.dropdownItemText}>No trip (general expense)</Text>
                </TouchableOpacity>
                {mockTrips.map(trip => (
                  <TouchableOpacity
                    key={trip.id}
                    style={styles.dropdownItem}
                    onPress={() => selectTrip(trip)}
                  >
                    <Text style={styles.dropdownItemText}>{trip.startLocation} → {trip.endLocation}</Text>
                    <Text style={styles.dropdownItemSubtext}>{trip.date} • {trip.distance} miles</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Receipt</Text>

          {expenseData.receiptImage ? (
            <View>
              <View style={styles.receiptImageContainer}>
                <Image source={{ uri: expenseData.receiptImage }} style={styles.receiptImage} />
                <TouchableOpacity style={styles.removeImageButton} onPress={removeImage}>
                  <Ionicons name="close-circle" size={24} color="#FF5252" />
                </TouchableOpacity>
              </View>

              <TouchableOpacity 
                style={styles.extractDataButton}
                onPress={() => processReceiptWithOcr(expenseData.receiptImage)}
                disabled={isProcessingOcr}
              >
                {isProcessingOcr ? (
                  <View style={styles.extractDataButtonContent}>
                    <ActivityIndicator size="small" color="#FFFFFF" />
                    <Text style={styles.extractDataButtonText}>Processing...</Text>
                  </View>
                ) : (
                  <View style={styles.extractDataButtonContent}>
                    <Ionicons name="scan-outline" size={20} color="#FFFFFF" />
                    <Text style={styles.extractDataButtonText}>Extract Data with OCR</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.receiptButtons}>
              <TouchableOpacity style={styles.receiptButton} onPress={takePhoto}>
                <Ionicons name="camera-outline" size={24} color="#4CAF50" />
                <Text style={styles.receiptButtonText}>Take Photo</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.receiptButton} onPress={pickImage}>
                <Ionicons name="image-outline" size={24} color="#4CAF50" />
                <Text style={styles.receiptButtonText}>Upload Image</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Additional Details</Text>

          <View style={styles.switchContainer}>
            <View style={styles.switchInfo}>
              <Text style={styles.switchLabel}>Tax Deductible</Text>
              <Text style={styles.switchDescription}>This expense is tax deductible for business purposes</Text>
            </View>
            <Switch
              value={expenseData.isTaxDeductible}
              onValueChange={toggleTaxDeductible}
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={expenseData.isTaxDeductible ? '#4CAF50' : '#f4f3f4'}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={expenseData.notes}
              onChangeText={(text) => handleChange('notes', text)}
              placeholder="Add any additional notes about this expense"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.saveButton} onPress={handleSubmit}>
            <Text style={styles.saveButtonText}>Save Expense</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.requiredFieldsNote}>
          <Text style={styles.requiredFieldsText}>* Required fields</Text>
        </View>
      </ScrollView>

      {/* OCR Results Modal */}
      <Modal
        visible={showOcrModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowOcrModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>AI-Assisted Receipt Analysis</Text>
              <TouchableOpacity onPress={() => setShowOcrModal(false)}>
                <Ionicons name="close" size={24} color="#757575" />
              </TouchableOpacity>
            </View>

            {editedOcrResults && (
              <ScrollView style={styles.ocrResultsContainer}>
                <Text style={styles.ocrMerchantName}>{editedOcrResults.merchantName}</Text>
                <View style={styles.confidenceIndicator}>
                  <View 
                    style={[
                      styles.confidenceBar, 
                      { width: `${editedOcrResults.confidenceLevels.merchantName * 100}%` }
                    ]} 
                  />
                  <Text style={styles.confidenceText}>
                    {Math.round(editedOcrResults.confidenceLevels.merchantName * 100)}% confidence
                  </Text>
                </View>

                {/* Amount field - editable */}
                <View style={styles.ocrResultItem}>
                  <Text style={styles.ocrResultLabel}>Amount:</Text>
                  <View style={styles.ocrResultEditContainer}>
                    <TextInput
                      style={styles.ocrResultEditInput}
                      value={editedOcrResults.amount}
                      onChangeText={(text) => handleOcrFieldEdit('amount', text)}
                      keyboardType="decimal-pad"
                    />
                    <View style={styles.confidenceIndicator}>
                      <View 
                        style={[
                          styles.confidenceBar, 
                          { width: `${editedOcrResults.confidenceLevels.amount * 100}%` }
                        ]} 
                      />
                      <Text style={styles.confidenceText}>
                        {Math.round(editedOcrResults.confidenceLevels.amount * 100)}% confidence
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Date field - editable */}
                <View style={styles.ocrResultItem}>
                  <Text style={styles.ocrResultLabel}>Date:</Text>
                  <View style={styles.ocrResultEditContainer}>
                    <TextInput
                      style={styles.ocrResultEditInput}
                      value={editedOcrResults.date}
                      onChangeText={(text) => handleOcrFieldEdit('date', text)}
                    />
                    <View style={styles.confidenceIndicator}>
                      <View 
                        style={[
                          styles.confidenceBar, 
                          { width: `${editedOcrResults.confidenceLevels.date * 100}%` }
                        ]} 
                      />
                      <Text style={styles.confidenceText}>
                        {Math.round(editedOcrResults.confidenceLevels.date * 100)}% confidence
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Category field - with suggestions */}
                <View style={styles.ocrResultItem}>
                  <Text style={styles.ocrResultLabel}>Category:</Text>
                  <View style={styles.ocrResultEditContainer}>
                    <View style={styles.categoryContainer}>
                      <TouchableOpacity 
                        style={[
                          styles.categoryChip,
                          styles.categoryChipSelected
                        ]}
                      >
                        <Text style={styles.categoryChipTextSelected}>{editedOcrResults.category}</Text>
                      </TouchableOpacity>

                      {editedOcrResults.alternativeCategories.map((category, index) => (
                        <TouchableOpacity 
                          key={index}
                          style={styles.categoryChip}
                          onPress={() => selectAlternativeCategory(category)}
                        >
                          <Text style={styles.categoryChipText}>{category}</Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                    <View style={styles.confidenceIndicator}>
                      <View 
                        style={[
                          styles.confidenceBar, 
                          { width: `${editedOcrResults.confidenceLevels.category * 100}%` }
                        ]} 
                      />
                      <Text style={styles.confidenceText}>
                        {Math.round(editedOcrResults.confidenceLevels.category * 100)}% confidence
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Description field - editable */}
                <View style={styles.ocrResultItem}>
                  <Text style={styles.ocrResultLabel}>Description:</Text>
                  <View style={styles.ocrResultEditContainer}>
                    <TextInput
                      style={styles.ocrResultEditInput}
                      value={editedOcrResults.description}
                      onChangeText={(text) => handleOcrFieldEdit('description', text)}
                      multiline
                    />
                    <View style={styles.confidenceIndicator}>
                      <View 
                        style={[
                          styles.confidenceBar, 
                          { width: `${editedOcrResults.confidenceLevels.description * 100}%` }
                        ]} 
                      />
                      <Text style={styles.confidenceText}>
                        {Math.round(editedOcrResults.confidenceLevels.description * 100)}% confidence
                      </Text>
                    </View>
                  </View>
                </View>

                <Text style={styles.aiAssistanceNote}>
                  Our AI has analyzed your receipt and extracted the information above.
                  Confidence levels indicate how certain the AI is about each field.
                  You can edit any field before applying.
                </Text>
              </ScrollView>
            )}

            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.modalSecondaryButton} 
                onPress={() => setShowOcrModal(false)}
              >
                <Text style={styles.modalSecondaryButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.modalPrimaryButton} 
                onPress={applyOcrResults}
              >
                <Text style={styles.modalPrimaryButtonText}>Apply Data</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginBottom: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#212121',
  },
  inputGroup: {
    marginBottom: 15,
    position: 'relative',
  },
  label: {
    fontSize: 16,
    color: '#212121',
    marginBottom: 5,
  },
  input: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  inputError: {
    borderColor: '#FF5252',
  },
  errorText: {
    color: '#FF5252',
    fontSize: 12,
    marginTop: 5,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  dropdownButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownButtonText: {
    fontSize: 16,
    color: '#212121',
  },
  dropdownButtonPlaceholder: {
    fontSize: 16,
    color: '#9E9E9E',
  },
  dropdown: {
    position: 'absolute',
    top: 80,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxHeight: 200,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#212121',
  },
  dropdownItemSubtext: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  receiptButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  receiptButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 0.48,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderStyle: 'dashed',
  },
  receiptButtonText: {
    color: '#4CAF50',
    marginTop: 8,
    fontWeight: '500',
  },
  receiptImageContainer: {
    position: 'relative',
    alignItems: 'center',
    marginBottom: 10,
  },
  receiptImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  removeImageButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 15,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    marginBottom: 15,
  },
  switchInfo: {
    flex: 1,
    marginRight: 10,
  },
  switchLabel: {
    fontSize: 16,
    color: '#212121',
    marginBottom: 3,
  },
  switchDescription: {
    fontSize: 12,
    color: '#757575',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 15,
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 0.48,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 0.48,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  cancelButtonText: {
    color: '#757575',
    fontSize: 16,
  },
  requiredFieldsNote: {
    margin: 15,
    marginTop: 0,
  },
  requiredFieldsText: {
    color: '#757575',
    fontSize: 12,
  },
  extractDataButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  extractDataButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  extractDataButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    width: '100%',
    maxWidth: 500,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  ocrResultsContainer: {
    marginBottom: 20,
  },
  ocrMerchantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 15,
    textAlign: 'center',
  },
  ocrResultItem: {
    flexDirection: 'row',
    marginBottom: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  ocrResultLabel: {
    width: 100,
    fontSize: 16,
    color: '#757575',
  },
  ocrResultValue: {
    flex: 1,
    fontSize: 16,
    color: '#212121',
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  modalPrimaryButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 0.48,
    alignItems: 'center',
  },
  modalPrimaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalSecondaryButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 0.48,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  modalSecondaryButtonText: {
    color: '#757575',
    fontSize: 16,
  },
  // New styles for enhanced OCR UI
  confidenceIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
    height: 6,
    backgroundColor: '#F0F0F0',
    borderRadius: 3,
    overflow: 'hidden',
    width: '100%',
  },
  confidenceBar: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 3,
  },
  confidenceText: {
    fontSize: 12,
    color: '#757575',
    marginLeft: 8,
  },
  ocrResultEditContainer: {
    flex: 1,
  },
  ocrResultEditInput: {
    fontSize: 16,
    color: '#212121',
    fontWeight: '500',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    padding: 8,
    backgroundColor: '#F9F9F9',
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 5,
  },
  categoryChip: {
    backgroundColor: '#F0F0F0',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  categoryChipSelected: {
    backgroundColor: '#E8F5E9',
    borderColor: '#4CAF50',
  },
  categoryChipText: {
    color: '#757575',
    fontSize: 14,
  },
  categoryChipTextSelected: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: '500',
  },
  aiAssistanceNote: {
    fontSize: 14,
    color: '#757575',
    fontStyle: 'italic',
    marginTop: 15,
    marginBottom: 10,
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  // AI suggestion styles
  aiProcessingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginLeft: 5,
  },
  confidenceIndicator: {
    height: 6,
    backgroundColor: '#F0F0F0',
    borderRadius: 3,
    overflow: 'hidden',
    width: '100%',
    marginTop: 5,
    marginBottom: 3,
  },
  confidenceBar: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 3,
  },
  confidenceText: {
    fontSize: 12,
    color: '#757575',
  },
  categoryChip: {
    backgroundColor: '#F0F0F0',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  categoryChipSelected: {
    backgroundColor: '#E8F5E9',
    borderColor: '#4CAF50',
  },
  categoryChipText: {
    color: '#757575',
    fontSize: 14,
  },
  categoryChipTextSelected: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: '500',
  },
  aiProcessingText: {
    fontSize: 12,
    color: '#4CAF50',
    marginLeft: 8,
  },
  aiSuggestionsContainer: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  aiSuggestionsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  aiSuggestionsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4CAF50',
    marginLeft: 6,
  },
  aiSuggestionItem: {
    marginBottom: 10,
  },
  alternativesContainer: {
    marginTop: 5,
    marginBottom: 10,
  },
  alternativesLabel: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 5,
  },
  alternativeChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  aiSuggestionsFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  applySuggestionButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 4,
    paddingVertical: 6,
    paddingHorizontal: 12,
    flex: 0.48,
    alignItems: 'center',
  },
  applySuggestionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  dismissSuggestionButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 4,
    paddingVertical: 6,
    paddingHorizontal: 12,
    flex: 0.48,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  dismissSuggestionButtonText: {
    color: '#757575',
    fontSize: 12,
  },
});

export default AddExpenseScreen;
