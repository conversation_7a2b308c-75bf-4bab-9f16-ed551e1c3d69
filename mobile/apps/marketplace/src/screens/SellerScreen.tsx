import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image, 
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for seller stats
const SELLER_STATS = {
  totalSales: 1245.99,
  pendingOrders: 3,
  activeListings: 12,
  averageRating: 4.8,
  totalReviews: 27,
  storeName: 'Green Valley Farm',
  storeDescription: 'Organic produce and farm-fresh goods directly from our family farm.',
  storeImage: 'https://via.placeholder.com/150',
};

// Mock data for recent orders
const RECENT_ORDERS = [
  { 
    id: '1', 
    orderNumber: 'ORD-2023-001', 
    date: '2023-07-01', 
    status: 'pending', 
    total: 45.99, 
    items: 3,
    buyer: '<PERSON>'
  },
  { 
    id: '2', 
    orderNumber: 'ORD-2023-002', 
    date: '2023-06-29', 
    status: 'processing', 
    total: 32.50, 
    items: 2,
    buyer: 'Sarah Johnson'
  },
  { 
    id: '3', 
    orderNumber: 'ORD-2023-003', 
    date: '2023-06-28', 
    status: 'shipped', 
    total: 78.25, 
    items: 5,
    buyer: 'Michael Brown'
  },
];

// Mock data for top products
const TOP_PRODUCTS = [
  { id: '1', name: 'Organic Tomatoes', price: 3.99, image: 'https://via.placeholder.com/150', sales: 42 },
  { id: '2', name: 'Fresh Eggs (Dozen)', price: 4.50, image: 'https://via.placeholder.com/150', sales: 38 },
  { id: '3', name: 'Honey (16oz)', price: 8.75, image: 'https://via.placeholder.com/150', sales: 25 },
];

const SellerScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [sellerStats, setSellerStats] = useState(SELLER_STATS);
  const [recentOrders, setRecentOrders] = useState(RECENT_ORDERS);
  const [topProducts, setTopProducts] = useState(TOP_PRODUCTS);
  const [isRegisteredSeller, setIsRegisteredSeller] = useState(true); // Set to false to show onboarding

  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refreshing data
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const handleSellerDashboardPress = () => {
    navigation.navigate('SellerDashboard');
  };

  const handleSellerProductsPress = () => {
    navigation.navigate('SellerProducts');
  };

  const handleSellerOrdersPress = () => {
    navigation.navigate('SellerOrders');
  };

  const handleAddProductPress = () => {
    navigation.navigate('AddProduct');
  };

  const handleProductPress = (productId: string) => {
    navigation.navigate('EditProduct', { productId });
  };

  const handleOrderPress = (orderId: string) => {
    navigation.navigate('OrderDetail', { orderId });
  };

  const handleMessagesPress = () => {
    navigation.navigate('Messages');
  };

  const handleReviewsPress = () => {
    navigation.navigate('Reviews', { sellerId: 'current-seller-id' });
  };

  const handleRegisterAsSeller = () => {
    // In a real app, this would navigate to a seller registration flow
    // For this demo, we'll just set isRegisteredSeller to true
    setIsRegisteredSeller(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#f59e0b'; // Amber
      case 'processing':
        return '#3b82f6'; // Blue
      case 'shipped':
        return '#8b5cf6'; // Purple
      case 'delivered':
        return '#10b981'; // Green
      case 'cancelled':
        return '#ef4444'; // Red
      default:
        return '#6b7280'; // Gray
    }
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  // Show seller onboarding if not registered
  if (!isRegisteredSeller) {
    return (
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.onboardingContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <Image 
          source={require('../../../../assets/seller-onboarding.png')} 
          style={styles.onboardingImage}
          defaultSource={{ uri: 'https://via.placeholder.com/300' }}
        />
        <Text style={styles.onboardingTitle}>Become a Seller</Text>
        <Text style={styles.onboardingDescription}>
          Turn your farm products into profit by selling directly to customers. 
          Set up your store, list your products, and start selling today.
        </Text>
        <View style={styles.benefitsContainer}>
          <View style={styles.benefitItem}>
            <Ionicons name="cash-outline" size={24} color="#3b82f6" />
            <Text style={styles.benefitText}>Earn additional income</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons name="people-outline" size={24} color="#3b82f6" />
            <Text style={styles.benefitText}>Reach more customers</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons name="stats-chart-outline" size={24} color="#3b82f6" />
            <Text style={styles.benefitText}>Track sales and growth</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons name="calendar-outline" size={24} color="#3b82f6" />
            <Text style={styles.benefitText}>Flexible scheduling</Text>
          </View>
        </View>
        <TouchableOpacity 
          style={styles.registerButton}
          onPress={handleRegisterAsSeller}
        >
          <Text style={styles.registerButtonText}>Register as Seller</Text>
        </TouchableOpacity>
        <Text style={styles.termsText}>
          By registering, you agree to our Terms of Service and Seller Agreement.
        </Text>
      </ScrollView>
    );
  }

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Seller Header */}
      <View style={styles.sellerHeader}>
        <Image 
          source={{ uri: sellerStats.storeImage }} 
          style={styles.sellerImage} 
        />
        <View style={styles.sellerInfo}>
          <Text style={styles.sellerName}>{sellerStats.storeName}</Text>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color="#f59e0b" />
            <Text style={styles.ratingText}>{sellerStats.averageRating} ({sellerStats.totalReviews} reviews)</Text>
          </View>
          <Text style={styles.sellerDescription} numberOfLines={2}>
            {sellerStats.storeDescription}
          </Text>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={handleAddProductPress}
        >
          <Ionicons name="add-circle" size={24} color="#3b82f6" />
          <Text style={styles.actionButtonText}>Add Product</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={handleMessagesPress}
        >
          <Ionicons name="chatbubbles" size={24} color="#3b82f6" />
          <Text style={styles.actionButtonText}>Messages</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={handleReviewsPress}
        >
          <Ionicons name="star" size={24} color="#3b82f6" />
          <Text style={styles.actionButtonText}>Reviews</Text>
        </TouchableOpacity>
      </View>

      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statsCard}>
          <Text style={styles.statsValue}>${sellerStats.totalSales.toFixed(2)}</Text>
          <Text style={styles.statsLabel}>Total Sales</Text>
        </View>
        <View style={styles.statsCard}>
          <Text style={styles.statsValue}>{sellerStats.pendingOrders}</Text>
          <Text style={styles.statsLabel}>Pending Orders</Text>
        </View>
        <View style={styles.statsCard}>
          <Text style={styles.statsValue}>{sellerStats.activeListings}</Text>
          <Text style={styles.statsLabel}>Active Listings</Text>
        </View>
      </View>

      {/* Main Menu */}
      <View style={styles.menuContainer}>
        <TouchableOpacity 
          style={styles.menuItem}
          onPress={handleSellerDashboardPress}
        >
          <Ionicons name="speedometer-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Seller Dashboard</Text>
            <Text style={styles.menuDescription}>View sales analytics and performance</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.menuItem}
          onPress={handleSellerProductsPress}
        >
          <Ionicons name="grid-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>My Products</Text>
            <Text style={styles.menuDescription}>Manage your product listings</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.menuItem}
          onPress={handleSellerOrdersPress}
        >
          <Ionicons name="list-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Orders</Text>
            <Text style={styles.menuDescription}>Manage customer orders</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.menuItem}
          onPress={handleMessagesPress}
        >
          <Ionicons name="chatbubbles-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Messages</Text>
            <Text style={styles.menuDescription}>Chat with customers</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.menuItem}
          onPress={handleReviewsPress}
        >
          <Ionicons name="star-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Reviews</Text>
            <Text style={styles.menuDescription}>View and respond to customer reviews</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      </View>

      {/* Recent Orders */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Orders</Text>
          <TouchableOpacity onPress={handleSellerOrdersPress}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        {recentOrders.map(order => (
          <TouchableOpacity 
            key={order.id} 
            style={styles.orderItem}
            onPress={() => handleOrderPress(order.id)}
          >
            <View style={styles.orderItemHeader}>
              <Text style={styles.orderNumber}>{order.orderNumber}</Text>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                <Text style={styles.statusText}>{order.status.charAt(0).toUpperCase() + order.status.slice(1)}</Text>
              </View>
            </View>
            <View style={styles.orderItemDetails}>
              <Text style={styles.orderItemDetail}>Date: {formatDate(order.date)}</Text>
              <Text style={styles.orderItemDetail}>Buyer: {order.buyer}</Text>
              <Text style={styles.orderItemDetail}>Items: {order.items}</Text>
              <Text style={styles.orderItemDetail}>Total: ${order.total.toFixed(2)}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Top Products */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Top Products</Text>
          <TouchableOpacity onPress={handleSellerProductsPress}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        {topProducts.map(product => (
          <TouchableOpacity 
            key={product.id} 
            style={styles.productItem}
            onPress={() => handleProductPress(product.id)}
          >
            <Image source={{ uri: product.image }} style={styles.productImage} />
            <View style={styles.productItemDetails}>
              <Text style={styles.productName}>{product.name}</Text>
              <Text style={styles.productPrice}>${product.price.toFixed(2)}</Text>
              <Text style={styles.productSales}>{product.sales} sold</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
        ))}
      </View>

      {/* Bottom padding */}
      <View style={{ height: 20 }} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sellerHeader: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  sellerImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 15,
  },
  sellerInfo: {
    flex: 1,
  },
  sellerName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  ratingText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  sellerDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  quickActions: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 12,
    marginTop: 5,
    color: '#3b82f6',
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: '#fff',
    marginTop: 10,
  },
  statsCard: {
    flex: 1,
    alignItems: 'center',
    padding: 10,
  },
  statsValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  statsLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  menuContainer: {
    backgroundColor: '#fff',
    marginTop: 10,
    paddingVertical: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  menuIcon: {
    marginRight: 15,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  menuDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  sectionContainer: {
    backgroundColor: '#fff',
    marginTop: 10,
    paddingVertical: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  seeAllText: {
    fontSize: 14,
    color: '#3b82f6',
  },
  orderItem: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  orderItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  orderItemDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  orderItemDetail: {
    fontSize: 12,
    color: '#666',
    marginRight: 10,
    marginBottom: 4,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  productImage: {
    width: 50,
    height: 50,
    borderRadius: 4,
    marginRight: 15,
  },
  productItemDetails: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
  },
  productPrice: {
    fontSize: 14,
    color: '#3b82f6',
    marginTop: 2,
  },
  productSales: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  onboardingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  onboardingImage: {
    width: 200,
    height: 200,
    marginBottom: 20,
  },
  onboardingTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  onboardingDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  benefitsContainer: {
    width: '100%',
    marginBottom: 20,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  benefitText: {
    fontSize: 16,
    marginLeft: 10,
  },
  registerButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    marginBottom: 15,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  termsText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});

export default SellerScreen;