
import Farm from './Farm.js';
import User from './User.js';
import UserFarm from './UserFarm.js';
import InventoryItem from './InventoryItem.js';
import InventoryCategory from './InventoryCategory.js';
import InventoryTransaction from './InventoryTransaction.js';
import Product from './Product.js';
import SeedProduct from './SeedProduct.js';
import ChemicalProduct from './ChemicalProduct.js';
import Equipment from './Equipment.js';
import EquipmentSharing from './EquipmentSharing.js';
import EquipmentTelematics from './EquipmentTelematics.js';
import Document from './Document.js';
import DocumentFolder from './DocumentFolder.js';
import DocumentPermission from './DocumentPermission.js';
import Livestock from './Livestock.js';
import LivestockGroup from './LivestockGroup.js';
import Crop from './Crop.js';
import CropActivity from './CropActivity.js';
import IoTDevice from './IoTDevice.js';
import IoTData from './IoTData.js';
import Integration from './Integration.js';
import Role from './Role.js';
import RolePermission from './RolePermission.js';
import Supplier from './Supplier.js';
import Order from './Order.js';
import OrderItem from './OrderItem.js';
import ServiceProvider from './ServiceProvider.js';
import ServiceRequest from './ServiceRequest.js';
import Vet from './Vet.js';
import Vendor from './Vendor.js';
import Field from './Field.js';
import SupportTicket from './SupportTicket.js';
import SupportTicketComment from './SupportTicketComment.js';
import SupportTicketAttachment from './SupportTicketAttachment.js';
import HelpGuide from './HelpGuide.js';
import HelpTip from './HelpTip.js';
import UserHelpTipDismissal from './UserHelpTipDismissal.js';
import PasswordGroup from './PasswordGroup.js';
import Password from './Password.js';
import PasswordGroupPermission from './PasswordGroupPermission.js';
import UserRecoveryKey from './UserRecoveryKey.js';
import RefreshToken from './RefreshToken.js';
import ScriptExecution from './ScriptExecution.js';

import MigrationSystem from './MigrationSystem.js';
import MigrationJob from './MigrationJob.js';
import MigrationResult from './MigrationResult.js';
import DatabaseMigration from './DatabaseMigration.js';
import Receipt from './Receipt.js';
import Driver from './Driver.js';
import Delivery from './Delivery.js';
import Pickup from './Pickup.js';
import DriverSchedule from './DriverSchedule.js';
import DriverLocation from './DriverLocation.js';
import Customer from './Customer.js';
import Expense from './Expense.js';
import CropType from './CropType.js';
import Harvest from './Harvest.js';
import MarketContract from './MarketContract.js';
import PriceComparison from './PriceComparison.js';
import MarketTrend from './MarketTrend.js';
import MarketplaceListing from './MarketplaceListing.js';
import SupplierProduct from "./SupplierProduct.js";
import SupplierReview from "./SupplierReview.js";
import DashboardLayout from './DashboardLayout.js';
import FarmDashboardLayout from './FarmDashboardLayout.js';
import GlobalDashboardLayout from './GlobalDashboardLayout.js';
import Alert from './Alert.js';
import AlertRule from './AlertRule.js';
import SubscriptionPlan from './SubscriptionPlan.js';
import Employee from './Employee.js';
import MarketPrice from './MarketPrice.js';
import FuturePrice from './FuturePrice.js';
import HistoricalPrice from './HistoricalPrice.js';
import HarvestDirectionMap from './HarvestDirectionMap.js';
import TaxCategory from './TaxCategory.js';
import TaxDeduction from './TaxDeduction.js';
import TaxDocument from './TaxDocument.js';
import EmployeeTaxInfo from './EmployeeTaxInfo.js';
import ContractorTaxInfo from './ContractorTaxInfo.js';
import TaxPayment from './TaxPayment.js';
import TaxFiling from './TaxFiling.js';
import SignableDocument from './SignableDocument.js';
import DocumentSigner from './DocumentSigner.js';
import DocumentSignature from './DocumentSignature.js';
import DocumentField from './DocumentField.js';
import DocumentAuditLog from './DocumentAuditLog.js';
import DocumentBlockchainVerification from './DocumentBlockchainVerification.js';
import DigitalCertificate from './DigitalCertificate.js';
import Invoice from './Invoice.js';
import InvoiceItem from './InvoiceItem.js';
import InvoiceQuestion from './InvoiceQuestion.js';
import InvoiceDispute from './InvoiceDispute.js';
import InvoiceDisputeMessage from './InvoiceDisputeMessage.js';
import InvoiceAuditLog from './InvoiceAuditLog.js';
import InvoiceNotification from './InvoiceNotification.js';
import InvoiceEmail from './InvoiceEmail.js';
import InvoiceDocument from './InvoiceDocument.js';
import Transaction from './Transaction.js';
import FarmFieldCollaboration from './FarmFieldCollaboration.js';
import CustomerNotification from './CustomerNotification.js';
import CustomerAddress from './CustomerAddress.js';
import BillCategory from './BillCategory.js';
import Bill from './Bill.js';
import RecurringBill from './RecurringBill.js';
import BillPayment from './BillPayment.js';
import BillTransaction from './BillTransaction.js';
import BillAttachment from './BillAttachment.js';
import FarmAssociation from './FarmAssociation.js';
import RecurringInvoice from './RecurringInvoice.js';
import ShoppingCart from './ShoppingCart.js';
import ShoppingCartItem from './ShoppingCartItem.js';
import PurchaseRequest from './PurchaseRequest.js';
import PurchaseRequestItem from './PurchaseRequestItem.js';
import FarmFulfillmentOptions from './FarmFulfillmentOptions.js';
import DeliverySchedule from './DeliverySchedule.js';
import DeliverySlot from './DeliverySlot.js';
import DeliveryRoute from './DeliveryRoute.js';
import DeliveryAssignment from './DeliveryAssignment.js';
import DeliveryTracking from './DeliveryTracking.js';
import FarmFeatureToggles from './FarmFeatureToggles.js';
import ProductImage from './ProductImage.js';

// Verify that critical associations are properly set up
export const verifyAssociations = () => {
  const criticalAssociations = [
    // Core system associations
    { model: User, association: 'userFarms', description: 'User -> UserFarms' },
    { model: Farm, association: 'userFarms', description: 'Farm -> UserFarms' },
    { model: UserFarm, association: 'user', description: 'UserFarm -> User' },
    { model: UserFarm, association: 'farm', description: 'UserFarm -> Farm' },

    // Invoice associations
    { model: Invoice, association: 'customer', description: 'Invoice -> Customer' },
    { model: Customer, association: 'invoices', description: 'Customer -> Invoices' },
    { model: Invoice, association: 'invoiceItems', description: 'Invoice -> InvoiceItems' },
    { model: InvoiceItem, association: 'invoice', description: 'InvoiceItem -> Invoice' },
    { model: Invoice, association: 'issuingFarm', description: 'Invoice -> IssuingFarm' },
    { model: Farm, association: 'issuedInvoices', description: 'Farm -> IssuedInvoices' },
    { model: RecurringInvoice, association: 'invoice', description: 'RecurringInvoice -> Invoice' },
    { model: Invoice, association: 'recurringSchedule', description: 'Invoice -> RecurringSchedule' },
    { model: RecurringBill, association: 'bill', description: 'RecurringBill -> Bill' },
    { model: Bill, association: 'recurringSchedule', description: 'Bill -> RecurringSchedule' },

    // Field and crop associations
    { model: Field, association: 'fieldFarm', description: 'Field -> Farm' },
    { model: Farm, association: 'fields', description: 'Farm -> Fields' },
    { model: Crop, association: 'cropFarm', description: 'Crop -> Farm' },
    { model: Farm, association: 'crops', description: 'Farm -> Crops' },

    // Inventory associations
    { model: InventoryItem, association: 'inventoryItemFarm', description: 'InventoryItem -> Farm' },
    { model: Farm, association: 'inventoryItems', description: 'Farm -> InventoryItems' },
    { model: InventoryItem, association: 'category', description: 'InventoryItem -> Category' },
    { model: InventoryCategory, association: 'items', description: 'InventoryCategory -> Items' },

    // Customer associations
    { model: Customer, association: 'customerFarm', description: 'Customer -> Farm' },
    { model: Farm, association: 'customers', description: 'Farm -> Customers' },
    { model: CustomerAddress, association: 'customer', description: 'CustomerAddress -> Customer' },
    { model: Customer, association: 'addresses', description: 'Customer -> Addresses' }
  ];

  console.log('🔍 Verifying critical associations...');

  criticalAssociations.forEach(({ model, association, description }) => {
    if (model.associations && model.associations[association]) {
      console.log(`✅ ${description} - OK`);
    } else {
      console.error(`❌ ${description} - MISSING`);
    }
  });
};

// Set up associations that require all models to be loaded first
// 
// IMPORTANT: All associations should be defined in this file to avoid duplicates and inconsistencies.
// Do not define associations directly in model files.
//
// Association Guidelines:
// 1. Group related associations together in logical sections
// 2. Use consistent naming for foreign keys and aliases
// 3. Specify onDelete behavior when appropriate
// 4. Use appropriate association types (belongsTo, hasMany, hasOne, belongsToMany)
// 5. Ensure both sides of associations are defined (e.g., if A belongsTo B, then B hasMany/hasOne A)
//
export const setupAssociations = () => {
  // ============================================================================
  // CORE SYSTEM ASSOCIATIONS (User, Farm, UserFarm, Roles, Subscriptions)
  // ============================================================================

  // Primary User and Farm many-to-many relationship - these must be defined first
  // as many other associations depend on them
  User.belongsToMany(Farm, { through: UserFarm, foreignKey: 'user_id' });
  Farm.belongsToMany(User, { through: UserFarm, foreignKey: 'farm_id' });

  // Direct associations with UserFarm junction table
  UserFarm.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(UserFarm, { foreignKey: 'user_id', as: 'userFarms', onDelete: 'CASCADE' });
  UserFarm.belongsTo(Farm, { foreignKey: 'farm_id', onDelete: 'CASCADE' });
  Farm.hasMany(UserFarm, { foreignKey: 'farm_id', as: 'userFarms', onDelete: 'CASCADE' });

  // Role system associations - depends on UserFarm
  UserFarm.belongsTo(Role, { foreignKey: 'role_id' });
  Role.hasMany(UserFarm, { foreignKey: 'role_id', as: 'userFarms' });

  // Subscription plan associations - depends on User and Farm
  User.belongsTo(SubscriptionPlan, { foreignKey: 'subscription_plan_id' });
  SubscriptionPlan.hasMany(User, { foreignKey: 'subscription_plan_id' });
  Farm.belongsTo(SubscriptionPlan, { foreignKey: 'subscription_plan_id' });
  SubscriptionPlan.hasMany(Farm, { foreignKey: 'subscription_plan_id' });

  // Farm-to-Farm associations - depends on Farm
  FarmAssociation.belongsTo(Farm, { foreignKey: 'initiator_farm_id', as: 'initiatorFarm', onDelete: 'CASCADE' });
  Farm.hasMany(FarmAssociation, { foreignKey: 'initiator_farm_id', as: 'initiatedAssociations', onDelete: 'CASCADE' });
  FarmAssociation.belongsTo(Farm, { foreignKey: 'associated_farm_id', as: 'associatedFarm', onDelete: 'CASCADE' });
  Farm.hasMany(FarmAssociation, { foreignKey: 'associated_farm_id', as: 'receivedAssociations', onDelete: 'CASCADE' });

  // Employee management associations - depends on User and Farm
  Employee.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(Employee, { foreignKey: 'farm_id', as: 'employees' });
  Employee.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Employee, { foreignKey: 'user_id', as: 'employeeRoles', onDelete: 'CASCADE' });

  // Refresh tokens for authentication - depends on User
  RefreshToken.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(RefreshToken, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // ============================================================================
  // INVENTORY & PRODUCT MANAGEMENT
  // ============================================================================

  // Inventory category relationships - depends on Farm
  InventoryCategory.belongsTo(Farm, { foreignKey: 'farm_id', as: 'inventoryCategoryFarm' });
  Farm.hasMany(InventoryCategory, { foreignKey: 'farm_id', as: 'categories' });

  // Inventory item relationships - depends on InventoryCategory and Farm
  InventoryItem.belongsTo(Farm, { foreignKey: 'farm_id', as: 'inventoryItemFarm' });
  Farm.hasMany(InventoryItem, { foreignKey: 'farm_id', as: 'inventoryItems' });
  InventoryItem.belongsTo(InventoryCategory, { foreignKey: 'category_id', as: 'category' });
  InventoryCategory.hasMany(InventoryItem, { foreignKey: 'category_id', as: 'items' });

  // Inventory transaction tracking - depends on InventoryItem and User
  InventoryTransaction.belongsTo(InventoryItem, { foreignKey: 'inventory_item_id', as: 'item' });
  InventoryItem.hasMany(InventoryTransaction, { foreignKey: 'inventory_item_id', as: 'transactions' });
  InventoryTransaction.belongsTo(User, { foreignKey: 'user_id' });
  User.hasMany(InventoryTransaction, { foreignKey: 'user_id', as: 'inventoryTransactions' });

  // Product management - depends on Farm
  Product.belongsTo(Farm, { foreignKey: 'farm_id', as: 'productFarm' });
  Farm.hasMany(Product, { foreignKey: 'farm_id', as: 'products' });

  // Product images - depends on Product
  ProductImage.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  Product.hasMany(ProductImage, { foreignKey: 'product_id', as: 'images' });

  // Specialized product types - depends on Product and Farm
  SeedProduct.belongsTo(Product, { foreignKey: 'product_id', as: 'seedProduct' });
  Product.hasOne(SeedProduct, { foreignKey: 'product_id' });
  SeedProduct.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(SeedProduct, { foreignKey: 'farm_id' });

  ChemicalProduct.belongsTo(Product, { foreignKey: 'product_id', as: 'chemicalProduct' });
  Product.hasOne(ChemicalProduct, { foreignKey: 'product_id' });
  ChemicalProduct.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(ChemicalProduct, { foreignKey: 'farm_id' });

  // ============================================================================
  // EQUIPMENT MANAGEMENT
  // ============================================================================

  // Equipment ownership and tracking
  Equipment.belongsTo(Farm, { foreignKey: 'farm_id', as: 'equipmentFarm' });
  Farm.hasMany(Equipment, { foreignKey: 'farm_id', as: 'equipment' });

  // Equipment telematics data
  Equipment.hasMany(EquipmentTelematics, { foreignKey: 'equipment_id', as: 'telematics' });
  EquipmentTelematics.belongsTo(Equipment, { foreignKey: 'equipment_id', as: 'telematicsEquipment' });

  // Equipment sharing between farms
  EquipmentSharing.belongsTo(Equipment, { foreignKey: 'equipment_id', as: 'sharingEquipment' });
  Equipment.hasMany(EquipmentSharing, { foreignKey: 'equipment_id', as: 'sharing' });
  EquipmentSharing.belongsTo(Farm, { as: 'Owner', foreignKey: 'owner_farm_id' });
  EquipmentSharing.belongsTo(Farm, { as: 'Renter', foreignKey: 'renter_farm_id' });
  Farm.hasMany(EquipmentSharing, { as: 'EquipmentLentOut', foreignKey: 'owner_farm_id' });
  Farm.hasMany(EquipmentSharing, { as: 'EquipmentBorrowed', foreignKey: 'renter_farm_id' });

  // ============================================================================
  // DOCUMENT MANAGEMENT SYSTEM
  // ============================================================================

  // Document folder structure - base entity for document organization
  DocumentFolder.belongsTo(Farm, { foreignKey: 'farm_id', as: 'documentFolderFarm' });
  Farm.hasMany(DocumentFolder, { foreignKey: 'farm_id', as: 'documentFolders' });
  DocumentFolder.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(DocumentFolder, { foreignKey: 'created_by', as: 'documentFolders' });

  // Self-referential association for parent-child folder structure
  DocumentFolder.belongsTo(DocumentFolder, { foreignKey: 'parent_folder_id', as: 'parentFolder' });
  DocumentFolder.hasMany(DocumentFolder, { foreignKey: 'parent_folder_id', as: 'subFolders' });

  // Document ownership and organization - depends on User, Farm, and DocumentFolder
  Document.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader', onDelete: 'CASCADE' });
  User.hasMany(Document, { foreignKey: 'uploaded_by', as: 'uploadedDocuments', onDelete: 'CASCADE' });
  Document.belongsTo(Farm, { foreignKey: 'farm_id', as: 'documentFarm' });
  Farm.hasMany(Document, { foreignKey: 'farm_id', as: 'documents' });
  Document.belongsTo(DocumentFolder, { foreignKey: 'folder_id', as: 'documentFolder' });
  DocumentFolder.hasMany(Document, { foreignKey: 'folder_id', as: 'folderDocuments' });

  // Document permissions and access control - depends on Document and User
  DocumentPermission.belongsTo(Document, { foreignKey: 'document_id', as: 'permissionDocument' });
  Document.hasMany(DocumentPermission, { foreignKey: 'document_id', as: 'permissions' });
  DocumentPermission.belongsTo(User, { foreignKey: 'user_id', as: 'permissionUser', onDelete: 'CASCADE' });
  User.hasMany(DocumentPermission, { foreignKey: 'user_id', as: 'documentPermissions', onDelete: 'CASCADE' });

  // ============================================================================
  // FARM OPERATIONS (Fields, Crops, Livestock, Harvests)
  // ============================================================================

  // Field management - fundamental entity for farm operations
  Field.belongsTo(Farm, { foreignKey: 'farm_id', as: 'fieldFarm' });
  Farm.hasMany(Field, { foreignKey: 'farm_id', as: 'fields' });

  // Field collaboration between farms - depends on Field and Farm
  FarmFieldCollaboration.belongsTo(Field, { foreignKey: 'field_id', as: 'field' });
  Field.hasMany(FarmFieldCollaboration, { foreignKey: 'field_id', as: 'collaborations' });
  FarmFieldCollaboration.belongsTo(Farm, { foreignKey: 'owner_farm_id', as: 'ownerFarm' });
  Farm.hasMany(FarmFieldCollaboration, { foreignKey: 'owner_farm_id', as: 'sharedFields' });
  FarmFieldCollaboration.belongsTo(Farm, { foreignKey: 'collaborator_farm_id', as: 'collaboratorFarm' });
  Farm.hasMany(FarmFieldCollaboration, { foreignKey: 'collaborator_farm_id', as: 'collaboratingFields' });

  // Harvest direction mapping - depends on Field
  HarvestDirectionMap.belongsTo(Field, { foreignKey: 'field_id', as: 'field' });
  Field.hasMany(HarvestDirectionMap, { foreignKey: 'field_id', as: 'harvestDirectionMaps' });

  // Crop types - base entity for crop management
  CropType.belongsTo(Farm, { foreignKey: 'farm_id', as: 'cropTypeFarm' });
  Farm.hasMany(CropType, { foreignKey: 'farm_id', as: 'cropTypes' });

  // Crop management - depends on Farm and CropType
  Crop.belongsTo(Farm, { foreignKey: 'farm_id', as: 'cropFarm' });
  Farm.hasMany(Crop, { foreignKey: 'farm_id', as: 'crops' });

  // Crop activities and tracking - depends on Crop
  CropActivity.belongsTo(Crop, { foreignKey: 'crop_id', as: 'crop' });
  Crop.hasMany(CropActivity, { foreignKey: 'crop_id', as: 'activities' });

  // Livestock management - depends on Farm
  Livestock.belongsTo(Farm, { foreignKey: 'farm_id', as: 'livestockFarm' });
  Farm.hasMany(Livestock, { foreignKey: 'farm_id', as: 'livestock' });
  LivestockGroup.belongsTo(Farm, { foreignKey: 'farm_id', as: 'livestockGroupFarm' });
  Farm.hasMany(LivestockGroup, { foreignKey: 'farm_id', as: 'livestockGroups' });

  // Harvest management - depends on Farm, Field, and Crop
  Harvest.belongsTo(Farm, { foreignKey: 'farm_id', as: 'harvestFarm' });
  Farm.hasMany(Harvest, { foreignKey: 'farm_id', as: 'harvests' });
  Harvest.belongsTo(Field, { foreignKey: 'field_id', as: 'harvestField' });
  Field.hasMany(Harvest, { foreignKey: 'field_id', as: 'harvests' });
  Harvest.belongsTo(Crop, { foreignKey: 'crop_id', as: 'harvestCrop' });
  Crop.hasMany(Harvest, { foreignKey: 'crop_id', as: 'harvests' });

  // ============================================================================
  // IOT & TECHNOLOGY INTEGRATIONS
  // ============================================================================

  // IoT device management and data collection
  IoTDevice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'iotDeviceFarm' });
  Farm.hasMany(IoTDevice, { foreignKey: 'farm_id', as: 'iotDevices' });
  IoTData.belongsTo(IoTDevice, { foreignKey: 'device_id', as: 'device' });
  IoTDevice.hasMany(IoTData, { foreignKey: 'device_id', as: 'data' });

  // Third-party integrations
  Integration.belongsTo(Farm, { foreignKey: 'farm_id', as: 'integrationFarm' });
  Farm.hasMany(Integration, { foreignKey: 'farm_id', as: 'integrations' });

  // ============================================================================
  // ALERTS & NOTIFICATIONS
  // ============================================================================

  // Alert system
  Alert.belongsTo(Farm, { foreignKey: 'farm_id' });
  Alert.belongsTo(User, { foreignKey: 'user_id' });
  Farm.hasMany(Alert, { foreignKey: 'farm_id', as: 'alerts' });
  User.hasMany(Alert, { foreignKey: 'user_id', as: 'alerts' });

  // Alert rule configuration
  AlertRule.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(AlertRule, { foreignKey: 'farm_id', as: 'alertRules' });

  // ============================================================================
  // PERMISSIONS & ACCESS CONTROL
  // ============================================================================

  // Role-based permissions
  RolePermission.belongsTo(Farm, { foreignKey: 'farm_id', as: 'rolePermissionFarm' });
  Farm.hasMany(RolePermission, { foreignKey: 'farm_id', as: 'farmRolePermissions' });

  // ============================================================================
  // SUPPLY CHAIN & VENDOR MANAGEMENT
  // ============================================================================

  // Supplier management
  Supplier.belongsTo(Farm, { foreignKey: 'farm_id', as: 'supplierFarm' });
  Farm.hasMany(Supplier, { foreignKey: 'farm_id', as: 'suppliers' });
  Supplier.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Supplier, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Supplier product catalog
  SupplierProduct.belongsTo(Supplier, { foreignKey: 'supplier_id' });
  Supplier.hasMany(SupplierProduct, { foreignKey: 'supplier_id' });
  SupplierProduct.belongsTo(Product, { foreignKey: 'product_id' });
  Product.hasMany(SupplierProduct, { foreignKey: 'product_id' });

  // Supplier reviews and ratings
  SupplierReview.belongsTo(Supplier, { foreignKey: 'supplier_id' });
  Supplier.hasMany(SupplierReview, { foreignKey: 'supplier_id' });
  SupplierReview.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(SupplierReview, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Order management
  Order.belongsTo(Farm, { foreignKey: 'farm_id', as: 'orderFarm' });
  Order.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'orderSupplier' });
  Order.belongsTo(User, { foreignKey: 'user_id', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(Order, { foreignKey: 'farm_id', as: 'orders' });
  Supplier.hasMany(Order, { foreignKey: 'supplier_id', as: 'supplierOrders' });
  User.hasMany(Order, { foreignKey: 'user_id', as: 'createdOrders', onDelete: 'CASCADE' });

  // Order items and inventory linking
  OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'orderItemOrder' });
  Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items' });
  OrderItem.belongsTo(InventoryItem, { foreignKey: 'inventory_item_id' });
  InventoryItem.hasMany(OrderItem, { foreignKey: 'inventory_item_id' });

  // Service provider management
  ServiceProvider.belongsTo(Farm, { foreignKey: 'farm_id', as: 'serviceProviderFarm' });
  Farm.hasMany(ServiceProvider, { foreignKey: 'farm_id', as: 'serviceProviders' });
  ServiceRequest.belongsTo(Farm, { foreignKey: 'farm_id', as: 'serviceRequestFarm' });
  ServiceRequest.belongsTo(ServiceProvider, { foreignKey: 'provider_id', as: 'provider' });
  Farm.hasMany(ServiceRequest, { foreignKey: 'farm_id', as: 'serviceRequests' });
  ServiceProvider.hasMany(ServiceRequest, { foreignKey: 'provider_id', as: 'providerServiceRequests' });

  // Veterinary services
  Vet.belongsTo(Farm, { foreignKey: 'farm_id', as: 'vetFarm' });
  Farm.hasMany(Vet, { foreignKey: 'farm_id', as: 'vets' });
  Vet.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Vet, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Vendor management
  Vendor.belongsTo(Farm, { foreignKey: 'farm_id', as: 'vendorFarm' });
  Farm.hasMany(Vendor, { foreignKey: 'farm_id', as: 'vendors' });
  Vendor.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Vendor, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // ============================================================================
  // SUPPORT & HELP SYSTEM
  // ============================================================================

  // Support ticket management
  SupportTicket.belongsTo(User, { foreignKey: 'user_id', as: 'creator', onDelete: 'CASCADE' });
  User.hasMany(SupportTicket, { foreignKey: 'user_id', as: 'createdTickets', onDelete: 'CASCADE' });
  SupportTicket.belongsTo(User, { foreignKey: 'assigned_to', as: 'assignee', onDelete: 'CASCADE' });
  User.hasMany(SupportTicket, { foreignKey: 'assigned_to', as: 'assignedTickets', onDelete: 'CASCADE' });
  SupportTicket.belongsTo(Farm, { foreignKey: 'farm_id', as: 'supportTicketFarm' });
  Farm.hasMany(SupportTicket, { foreignKey: 'farm_id' });

  // Support ticket comments
  SupportTicketComment.belongsTo(SupportTicket, { foreignKey: 'ticket_id' });
  SupportTicket.hasMany(SupportTicketComment, { foreignKey: 'ticket_id', as: 'comments' });
  SupportTicketComment.belongsTo(User, { foreignKey: 'user_id', as: 'author', onDelete: 'CASCADE' });
  User.hasMany(SupportTicketComment, { foreignKey: 'user_id', as: 'ticketComments', onDelete: 'CASCADE' });

  // Support ticket attachments
  SupportTicketAttachment.belongsTo(SupportTicket, { foreignKey: 'ticket_id' });
  SupportTicket.hasMany(SupportTicketAttachment, { foreignKey: 'ticket_id', as: 'attachments' });
  SupportTicketAttachment.belongsTo(User, { foreignKey: 'user_id', as: 'uploader', onDelete: 'CASCADE' });
  User.hasMany(SupportTicketAttachment, { foreignKey: 'user_id', as: 'uploadedAttachments', onDelete: 'CASCADE' });

  // Help tip system
  UserHelpTipDismissal.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(UserHelpTipDismissal, { foreignKey: 'user_id', as: 'dismissedHelpTips', onDelete: 'CASCADE' });
  UserHelpTipDismissal.belongsTo(HelpTip, { foreignKey: 'help_tip_id' });
  HelpTip.hasMany(UserHelpTipDismissal, { foreignKey: 'help_tip_id', as: 'userDismissals' });

  // ============================================================================
  // DATA MIGRATION & SYSTEM ADMINISTRATION
  // ============================================================================

  // Data migration system
  MigrationJob.belongsTo(MigrationSystem, { foreignKey: 'source_system', as: 'system' });
  MigrationSystem.hasMany(MigrationJob, { foreignKey: 'source_system', as: 'jobs' });
  MigrationJob.belongsTo(User, { foreignKey: 'user_id', as: 'migrationJobUser', onDelete: 'CASCADE' });
  MigrationJob.belongsTo(Farm, { foreignKey: 'farm_id', as: 'migrationJobFarm' });
  Farm.hasMany(MigrationJob, { foreignKey: 'farm_id', as: 'farmMigrationJobs' });
  User.hasMany(MigrationJob, { foreignKey: 'user_id', as: 'userMigrationJobs', onDelete: 'CASCADE' });

  // Migration results tracking
  MigrationResult.belongsTo(MigrationJob, { foreignKey: 'job_id', as: 'job' });
  MigrationJob.hasOne(MigrationResult, { foreignKey: 'job_id', as: 'result' });

  // Database schema migrations
  DatabaseMigration.belongsTo(User, { foreignKey: 'applied_by', as: 'appliedBy', onDelete: 'CASCADE' });
  User.hasMany(DatabaseMigration, { foreignKey: 'applied_by', as: 'appliedMigrations', onDelete: 'CASCADE' });

  // Script execution tracking
  ScriptExecution.belongsTo(User, { foreignKey: 'executed_by', as: 'executedBy', onDelete: 'SET NULL' });
  User.hasMany(ScriptExecution, { foreignKey: 'executed_by', as: 'executedScripts', onDelete: 'SET NULL' });

  // Dashboard customization
  DashboardLayout.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasOne(DashboardLayout, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  FarmDashboardLayout.belongsTo(Farm, { foreignKey: 'farm_id', onDelete: 'CASCADE' });
  Farm.hasOne(FarmDashboardLayout, { foreignKey: 'farm_id', onDelete: 'CASCADE' });

  // ============================================================================
  // FINANCIAL MANAGEMENT
  // ============================================================================

  // Expense tracking and receipts
  Expense.belongsTo(Employee, { foreignKey: 'employee_id' });
  Expense.belongsTo(User, { foreignKey: 'reviewed_by', as: 'reviewer', onDelete: 'CASCADE' });
  Employee.hasMany(Expense, { foreignKey: 'employee_id', as: 'expenses' });
  User.hasMany(Expense, { foreignKey: 'reviewed_by', as: 'reviewedExpenses', onDelete: 'CASCADE' });

  // Receipt management
  Receipt.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader', onDelete: 'CASCADE' });
  User.hasMany(Receipt, { foreignKey: 'uploaded_by', as: 'uploadedReceipts', onDelete: 'CASCADE' });
  Receipt.belongsTo(Farm, { foreignKey: 'farm_id', as: 'receiptFarm' });
  Farm.hasMany(Receipt, { foreignKey: 'farm_id', as: 'receipts' });
  Receipt.belongsTo(Expense, { foreignKey: 'expense_id', as: 'expense' });
  Expense.hasOne(Receipt, { foreignKey: 'expense_id', as: 'receipt' });

  // ============================================================================
  // CORE CUSTOMER MANAGEMENT
  // ============================================================================

  // Customer management
  Customer.belongsTo(Farm, { foreignKey: 'farm_id', as: 'customerFarm' });
  Farm.hasMany(Customer, { foreignKey: 'farm_id', as: 'customers' });

  // Customer addresses
  CustomerAddress.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  CustomerAddress.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Customer.hasMany(CustomerAddress, { foreignKey: 'customer_id', as: 'addresses' });
  Farm.hasMany(CustomerAddress, { foreignKey: 'farm_id', as: 'customerAddresses' });

  // Customer notifications
  CustomerNotification.belongsTo(Customer, { foreignKey: 'customer_id', onDelete: 'CASCADE' });
  Customer.hasMany(CustomerNotification, { foreignKey: 'customer_id', onDelete: 'CASCADE' });
  CustomerNotification.belongsTo(Farm, { foreignKey: 'farm_id', onDelete: 'CASCADE' });
  Farm.hasMany(CustomerNotification, { foreignKey: 'farm_id', onDelete: 'CASCADE' });

  // ============================================================================
  // TRANSPORT MANAGEMENT SYSTEM
  // ============================================================================

  // Driver management - fundamental entity for transport system
  Driver.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverFarm' });
  Farm.hasMany(Driver, { foreignKey: 'farm_id', as: 'drivers' });
  Driver.belongsTo(User, { foreignKey: 'user_id', as: 'driverUser', onDelete: 'CASCADE' });
  User.hasOne(Driver, { foreignKey: 'user_id', as: 'driverUser', onDelete: 'CASCADE' });

  // Driver location tracking - depends on Driver
  DriverLocation.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverLocationFarm' });
  Farm.hasMany(DriverLocation, { foreignKey: 'farm_id', as: 'driverLocations' });
  DriverLocation.belongsTo(Driver, { foreignKey: 'driver_id', as: 'locationDriver' });
  Driver.hasMany(DriverLocation, { foreignKey: 'driver_id', as: 'locations' });

  // Delivery scheduling system - base structure for delivery management
  DeliverySchedule.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(DeliverySchedule, { foreignKey: 'farm_id', as: 'delivery_schedules' });

  DeliverySlot.belongsTo(DeliverySchedule, { foreignKey: 'schedule_id', as: 'schedule' });
  DeliverySchedule.hasMany(DeliverySlot, { foreignKey: 'schedule_id', as: 'slots' });

  DeliveryRoute.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(DeliveryRoute, { foreignKey: 'farm_id', as: 'delivery_routes' });
  DeliveryRoute.belongsTo(User, { foreignKey: 'driver_id', as: 'driverUser' });
  User.hasMany(DeliveryRoute, { foreignKey: 'driver_id', as: 'assigned_routes' });

  // Delivery operations - depends on Driver, Farm, Customer, Order
  Delivery.belongsTo(Farm, { foreignKey: 'farm_id', as: 'deliveryFarm' });
  Farm.hasMany(Delivery, { foreignKey: 'farm_id', as: 'deliveries' });
  Delivery.belongsTo(Driver, { foreignKey: 'driver_id', as: 'driver' });
  Driver.hasMany(Delivery, { foreignKey: 'driver_id', as: 'deliveries' });
  Delivery.belongsTo(Customer, { foreignKey: 'customer_id', as: 'deliveryCustomer' });
  Customer.hasMany(Delivery, { foreignKey: 'customer_id', as: 'deliveries' });
  Delivery.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Order.hasOne(Delivery, { foreignKey: 'order_id', as: 'delivery' });

  // Pickup operations - depends on Driver, Farm, Supplier, Customer, Order
  Pickup.belongsTo(Farm, { foreignKey: 'farm_id', as: 'pickupFarm' });
  Farm.hasMany(Pickup, { foreignKey: 'farm_id', as: 'pickups' });
  Pickup.belongsTo(Driver, { foreignKey: 'driver_id', as: 'pickupDriver' });
  Driver.hasMany(Pickup, { foreignKey: 'driver_id', as: 'pickups' });
  Pickup.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'supplier' });
  Supplier.hasMany(Pickup, { foreignKey: 'supplier_id', as: 'pickups' });
  Pickup.belongsTo(Customer, { foreignKey: 'customer_id', as: 'pickupCustomer' });
  Customer.hasMany(Pickup, { foreignKey: 'customer_id', as: 'pickups' });
  Pickup.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Order.hasOne(Pickup, { foreignKey: 'order_id', as: 'pickup' });

  // Driver scheduling - depends on Driver, Delivery, Pickup
  DriverSchedule.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverScheduleFarm' });
  Farm.hasMany(DriverSchedule, { foreignKey: 'farm_id', as: 'driverSchedules' });
  DriverSchedule.belongsTo(Driver, { foreignKey: 'driver_id', as: 'driver' });
  Driver.hasMany(DriverSchedule, { foreignKey: 'driver_id', as: 'schedules' });
  DriverSchedule.belongsTo(Delivery, { foreignKey: 'delivery_id', as: 'delivery' });
  Delivery.hasOne(DriverSchedule, { foreignKey: 'delivery_id', as: 'deliverySchedule' });
  DriverSchedule.belongsTo(Pickup, { foreignKey: 'pickup_id', as: 'pickupAssignment' });
  Pickup.hasOne(DriverSchedule, { foreignKey: 'pickup_id', as: 'pickupSchedule' });

  // Delivery assignments and tracking - depends on DeliveryRoute and PurchaseRequest
  DeliveryAssignment.belongsTo(DeliveryRoute, { foreignKey: 'route_id', as: 'route' });
  DeliveryRoute.hasMany(DeliveryAssignment, { foreignKey: 'route_id', as: 'assignments' });
  DeliveryAssignment.belongsTo(PurchaseRequest, { foreignKey: 'purchase_request_id', as: 'purchase_request' });
  PurchaseRequest.hasOne(DeliveryAssignment, { foreignKey: 'purchase_request_id', as: 'delivery_assignment' });

  DeliveryTracking.belongsTo(DeliveryRoute, { foreignKey: 'route_id', as: 'route' });
  DeliveryRoute.hasMany(DeliveryTracking, { foreignKey: 'route_id', as: 'tracking_points' });

  // Purchase request delivery references - depends on DeliverySlot and DeliveryRoute
  PurchaseRequest.belongsTo(DeliverySlot, { foreignKey: 'delivery_slot_id', as: 'delivery_slot' });
  DeliverySlot.hasMany(PurchaseRequest, { foreignKey: 'delivery_slot_id', as: 'purchase_requests' });
  PurchaseRequest.belongsTo(DeliveryRoute, { foreignKey: 'delivery_route_id', as: 'delivery_route' });
  DeliveryRoute.hasMany(PurchaseRequest, { foreignKey: 'delivery_route_id', as: 'purchase_requests' });

  // ============================================================================
  // MARKET INTEGRATION & PRICING
  // ============================================================================

  // Market contracts
  MarketContract.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketContractFarm' });
  MarketContract.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'marketContractSupplier' });
  MarketContract.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(MarketContract, { foreignKey: 'farm_id', as: 'marketContracts' });
  Supplier.hasMany(MarketContract, { foreignKey: 'supplier_id', as: 'supplierContracts' });
  User.hasMany(MarketContract, { foreignKey: 'created_by', as: 'createdContracts', onDelete: 'CASCADE' });

  // Price comparison and analysis
  PriceComparison.belongsTo(Farm, { foreignKey: 'farm_id', as: 'priceComparisonFarm' });
  PriceComparison.belongsTo(Product, { foreignKey: 'product_id', as: 'priceComparisonProduct' });
  PriceComparison.belongsTo(Supplier, { foreignKey: 'best_price_supplier_id', as: 'bestPriceSupplier' });
  Farm.hasMany(PriceComparison, { foreignKey: 'farm_id', as: 'priceComparisons' });
  Product.hasMany(PriceComparison, { foreignKey: 'product_id', as: 'priceComparisons' });
  Supplier.hasMany(PriceComparison, { foreignKey: 'best_price_supplier_id', as: 'bestPriceComparisons' });

  // Market trends and analytics
  MarketTrend.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketTrendFarm' });
  MarketTrend.belongsTo(Product, { foreignKey: 'product_id', as: 'marketTrendProduct' });
  Farm.hasMany(MarketTrend, { foreignKey: 'farm_id', as: 'marketTrends' });
  Product.hasMany(MarketTrend, { foreignKey: 'product_id', as: 'marketTrends' });

  // Marketplace listings
  MarketplaceListing.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketplaceListingFarm' });
  MarketplaceListing.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  MarketplaceListing.belongsTo(Product, { foreignKey: 'product_id', as: 'marketplaceListingProduct' });
  Farm.hasMany(MarketplaceListing, { foreignKey: 'farm_id', as: 'marketplaceListings' });
  User.hasMany(MarketplaceListing, { foreignKey: 'created_by', as: 'createdListings', onDelete: 'CASCADE' });
  Product.hasMany(MarketplaceListing, { foreignKey: 'product_id', as: 'marketplaceListings' });

  // Shopping cart associations
  ShoppingCart.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  ShoppingCart.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  User.hasMany(ShoppingCart, { foreignKey: 'user_id', as: 'shoppingCarts' });
  Farm.hasMany(ShoppingCart, { foreignKey: 'farm_id', as: 'shoppingCarts' });

  // Shopping cart items
  ShoppingCart.hasMany(ShoppingCartItem, { foreignKey: 'cart_id', as: 'items' });
  ShoppingCartItem.belongsTo(ShoppingCart, { foreignKey: 'cart_id', as: 'cart' });
  ShoppingCartItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  Product.hasMany(ShoppingCartItem, { foreignKey: 'product_id', as: 'cartItems' });

  // Purchase request associations
  PurchaseRequest.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  PurchaseRequest.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  PurchaseRequest.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  User.hasMany(PurchaseRequest, { foreignKey: 'user_id', as: 'purchaseRequests' });
  Farm.hasMany(PurchaseRequest, { foreignKey: 'farm_id', as: 'purchaseRequests' });
  Customer.hasMany(PurchaseRequest, { foreignKey: 'customer_id', as: 'purchaseRequests' });

  // Farm Fulfillment Options associations
  FarmFulfillmentOptions.belongsTo(Farm, { foreignKey: 'farm_id', as: 'fulfillmentFarm', onDelete: 'CASCADE' });
  Farm.hasMany(FarmFulfillmentOptions, { foreignKey: 'farm_id', as: 'fulfillmentOptions', onDelete: 'CASCADE' });

  // Farm Feature Toggles associations
  FarmFeatureToggles.belongsTo(Farm, { foreignKey: 'farm_id', as: 'togglesFarm', onDelete: 'CASCADE' });
  Farm.hasOne(FarmFeatureToggles, { foreignKey: 'farm_id', as: 'featureToggles', onDelete: 'CASCADE' });

  // Purchase request items
  PurchaseRequestItem.belongsTo(PurchaseRequest, { foreignKey: 'request_id', as: 'request' });
  PurchaseRequest.hasMany(PurchaseRequestItem, { foreignKey: 'request_id', as: 'items' });
  PurchaseRequestItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  Product.hasMany(PurchaseRequestItem, { foreignKey: 'product_id', as: 'purchaseRequestItems' });

  // Purchase request delivery address
  PurchaseRequest.belongsTo(CustomerAddress, { foreignKey: 'delivery_address_id', as: 'deliveryAddress' });
  CustomerAddress.hasMany(PurchaseRequest, { foreignKey: 'delivery_address_id', as: 'purchaseRequests' });

  // Price tracking (current, future, historical)
  MarketPrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketPriceFarm' });
  Farm.hasMany(MarketPrice, { foreignKey: 'farm_id', as: 'marketPrices' });
  FuturePrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'futurePriceFarm' });
  Farm.hasMany(FuturePrice, { foreignKey: 'farm_id', as: 'futurePrices' });
  HistoricalPrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'historicalPriceFarm' });
  Farm.hasMany(HistoricalPrice, { foreignKey: 'farm_id', as: 'historicalPrices' });

  // ============================================================================
  // TAX MANAGEMENT SYSTEM
  // ============================================================================

  // Tax categories and deductions
  TaxCategory.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxCategoryFarm' });
  Farm.hasMany(TaxCategory, { foreignKey: 'farm_id', as: 'taxCategories' });
  TaxDeduction.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxDeductionFarm' });
  Farm.hasMany(TaxDeduction, { foreignKey: 'farm_id', as: 'taxDeductions' });

  // Tax document management
  TaxDocument.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxDocumentFarm' });
  TaxDocument.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(TaxDocument, { foreignKey: 'farm_id', as: 'taxDocuments' });
  User.hasMany(TaxDocument, { foreignKey: 'created_by', as: 'createdTaxDocuments', onDelete: 'CASCADE' });

  // Employee tax information (W-2 forms)
  EmployeeTaxInfo.belongsTo(Employee, { foreignKey: 'employee_id', as: 'employee' });
  EmployeeTaxInfo.belongsTo(Farm, { foreignKey: 'farm_id', as: 'employeeTaxInfoFarm' });
  EmployeeTaxInfo.belongsTo(TaxDocument, { foreignKey: 'w2_document_id', as: 'w2Document' });
  Employee.hasMany(EmployeeTaxInfo, { foreignKey: 'employee_id', as: 'taxInfo' });
  Farm.hasMany(EmployeeTaxInfo, { foreignKey: 'farm_id', as: 'employeeTaxInfo' });
  TaxDocument.hasOne(EmployeeTaxInfo, { foreignKey: 'w2_document_id', as: 'employeeTaxInfo' });

  // Contractor tax information (1099 forms)
  ContractorTaxInfo.belongsTo(Farm, { foreignKey: 'farm_id', as: 'contractorTaxInfoFarm' });
  ContractorTaxInfo.belongsTo(TaxDocument, { foreignKey: 'form_1099_document_id', as: 'form1099Document' });
  Farm.hasMany(ContractorTaxInfo, { foreignKey: 'farm_id', as: 'contractorTaxInfo' });
  TaxDocument.hasOne(ContractorTaxInfo, { foreignKey: 'form_1099_document_id', as: 'contractorTaxInfo' });

  // Tax payments and filings
  TaxPayment.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxPaymentFarm' });
  TaxPayment.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  TaxPayment.belongsTo(TaxDocument, { foreignKey: 'receipt_document_id', as: 'receiptDocument' });
  Farm.hasMany(TaxPayment, { foreignKey: 'farm_id', as: 'taxPayments' });
  User.hasMany(TaxPayment, { foreignKey: 'created_by', as: 'createdTaxPayments', onDelete: 'CASCADE' });
  TaxDocument.hasOne(TaxPayment, { foreignKey: 'receipt_document_id', as: 'taxPayment' });

  TaxFiling.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxFilingFarm' });
  TaxFiling.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  TaxFiling.belongsTo(TaxDocument, { foreignKey: 'document_id', as: 'filingDocument' });
  Farm.hasMany(TaxFiling, { foreignKey: 'farm_id', as: 'taxFilings' });
  User.hasMany(TaxFiling, { foreignKey: 'created_by', as: 'createdTaxFilings', onDelete: 'CASCADE' });
  TaxDocument.hasOne(TaxFiling, { foreignKey: 'document_id', as: 'taxFiling' });

  // ============================================================================
  // DOCUMENT SIGNING SYSTEM
  // ============================================================================

  // Signable document management
  SignableDocument.belongsTo(Farm, { foreignKey: 'farm_id', as: 'signableDocumentFarm' });
  SignableDocument.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Farm.hasMany(SignableDocument, { foreignKey: 'farm_id', as: 'signableDocuments' });
  User.hasMany(SignableDocument, { foreignKey: 'created_by', as: 'createdSignableDocuments' });

  // Document signers and signatures
  SignableDocument.hasMany(DocumentSigner, { foreignKey: 'document_id', as: 'signers' });
  SignableDocument.hasMany(DocumentSignature, { foreignKey: 'document_id', as: 'signatures' });
  DocumentSigner.hasMany(DocumentSignature, { foreignKey: 'signer_id', as: 'signatures' });

  // Document fields and form data
  SignableDocument.hasMany(DocumentField, { foreignKey: 'document_id', as: 'fields' });
  DocumentSigner.hasMany(DocumentField, { foreignKey: 'signer_id', as: 'fields' });

  // Document audit trail
  DocumentAuditLog.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'auditLogDocument' });
  DocumentAuditLog.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });
  DocumentAuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  SignableDocument.hasMany(DocumentAuditLog, { foreignKey: 'document_id', as: 'auditLogs' });
  DocumentSigner.hasMany(DocumentAuditLog, { foreignKey: 'signer_id', as: 'auditLogs' });
  User.hasMany(DocumentAuditLog, { foreignKey: 'user_id', as: 'documentAuditLogs' });

  // Blockchain verification for document integrity
  DocumentBlockchainVerification.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'blockchainVerificationDocument' });
  SignableDocument.hasMany(DocumentBlockchainVerification, { foreignKey: 'document_id', as: 'blockchainVerifications' });

  // Digital certificates for signing
  DigitalCertificate.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  DigitalCertificate.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  User.hasMany(DigitalCertificate, { foreignKey: 'user_id', as: 'digitalCertificates' });
  Farm.hasMany(DigitalCertificate, { foreignKey: 'farm_id', as: 'digitalCertificates' });

  // ============================================================================
  // INVOICING & BILLING
  // ============================================================================

  // Primary Invoice associations - these must be defined first
  // as many other invoice-related associations depend on them

  // Invoice to Farm relationships - depends on Farm
  Invoice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'issuingFarm', onDelete: 'CASCADE' });
  Farm.hasMany(Invoice, { foreignKey: 'farm_id', as: 'issuedInvoices', onDelete: 'CASCADE' });
  Invoice.belongsTo(Farm, { foreignKey: 'recipient_farm_id', as: 'recipientFarm', onDelete: 'CASCADE' });
  Farm.hasMany(Invoice, { foreignKey: 'recipient_farm_id', as: 'receivedInvoices', onDelete: 'CASCADE' });

  // Invoice to Customer relationship - depends on Customer
  Invoice.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer', onDelete: 'CASCADE' });
  Customer.hasMany(Invoice, { foreignKey: 'customer_id', as: 'invoices', onDelete: 'CASCADE' });

  // Invoice payment tracking - depends on Invoice and Transaction
  Invoice.belongsTo(Transaction, { foreignKey: 'payment_transaction_id', as: 'paymentTransaction', onDelete: 'SET NULL' });
  Transaction.hasMany(Invoice, { foreignKey: 'payment_transaction_id', as: 'paidInvoices', onDelete: 'SET NULL' });

  // Invoice line items - depends on Invoice and Product
  InvoiceItem.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceItem, { foreignKey: 'invoice_id', as: 'invoiceItems', onDelete: 'CASCADE' });
  InvoiceItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product', onDelete: 'SET NULL' });
  Product.hasMany(InvoiceItem, { foreignKey: 'product_id', as: 'invoiceItems', onDelete: 'SET NULL' });

  // Recurring invoice associations - depends on Invoice
  RecurringInvoice.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
  Invoice.hasOne(RecurringInvoice, { foreignKey: 'invoice_id', as: 'recurringSchedule', onDelete: 'CASCADE' });

  // Invoice documents - depends on Invoice and User
  InvoiceDocument.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceDocument, { foreignKey: 'invoice_id', as: 'documents', onDelete: 'CASCADE' });
  InvoiceDocument.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploadedByUser', onDelete: 'SET NULL' });
  User.hasMany(InvoiceDocument, { foreignKey: 'uploaded_by', as: 'uploadedInvoiceDocuments', onDelete: 'SET NULL' });

  // Invoice emails - depends on Invoice and User
  InvoiceEmail.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceEmail, { foreignKey: 'invoice_id', as: 'emails', onDelete: 'CASCADE' });
  InvoiceEmail.belongsTo(User, { foreignKey: 'sent_by_user_id', as: 'sentByUser', onDelete: 'SET NULL' });
  User.hasMany(InvoiceEmail, { foreignKey: 'sent_by_user_id', as: 'sentInvoiceEmails', onDelete: 'SET NULL' });

  // Invoice questions - depends on Invoice, Customer, and Farm
  InvoiceQuestion.belongsTo(Invoice, { foreignKey: 'invoice_id', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceQuestion, { foreignKey: 'invoice_id', onDelete: 'CASCADE' });
  InvoiceQuestion.belongsTo(Customer, { foreignKey: 'customer_id', onDelete: 'CASCADE' });
  Customer.hasMany(InvoiceQuestion, { foreignKey: 'customer_id', onDelete: 'CASCADE' });
  InvoiceQuestion.belongsTo(Farm, { foreignKey: 'farm_id', onDelete: 'CASCADE' });
  Farm.hasMany(InvoiceQuestion, { foreignKey: 'farm_id', onDelete: 'CASCADE' });

  // Invoice audit logs - depends on Invoice, User, and Farm
  InvoiceAuditLog.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceAuditLog, { foreignKey: 'invoice_id', as: 'auditLogs', onDelete: 'CASCADE' });
  InvoiceAuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user', onDelete: 'SET NULL' });
  User.hasMany(InvoiceAuditLog, { foreignKey: 'user_id', as: 'invoiceAuditLogs', onDelete: 'SET NULL' });
  InvoiceAuditLog.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm', onDelete: 'CASCADE' });
  Farm.hasMany(InvoiceAuditLog, { foreignKey: 'farm_id', as: 'invoiceAuditLogs', onDelete: 'CASCADE' });

  // Invoice disputes - depends on Invoice, Farm, and User
  InvoiceDispute.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceDispute, { foreignKey: 'invoice_id', as: 'disputes', onDelete: 'CASCADE' });
  InvoiceDispute.belongsTo(Farm, { foreignKey: 'raised_by_farm_id', as: 'raisedByFarm', onDelete: 'CASCADE' });
  Farm.hasMany(InvoiceDispute, { foreignKey: 'raised_by_farm_id', as: 'raisedDisputes', onDelete: 'CASCADE' });
  InvoiceDispute.belongsTo(User, { foreignKey: 'raised_by_user_id', as: 'raisedByUser', onDelete: 'CASCADE' });
  User.hasMany(InvoiceDispute, { foreignKey: 'raised_by_user_id', as: 'raisedDisputes', onDelete: 'CASCADE' });
  InvoiceDispute.belongsTo(User, { foreignKey: 'resolved_by_user_id', as: 'resolvedByUser', onDelete: 'SET NULL' });
  User.hasMany(InvoiceDispute, { foreignKey: 'resolved_by_user_id', as: 'resolvedDisputes', onDelete: 'SET NULL' });
  InvoiceDispute.belongsTo(User, { foreignKey: 'escalated_to_user_id', as: 'escalatedToUser', onDelete: 'SET NULL' });
  User.hasMany(InvoiceDispute, { foreignKey: 'escalated_to_user_id', as: 'escalatedDisputes', onDelete: 'SET NULL' });

  // Invoice dispute messages - depends on InvoiceDispute, User, and Farm
  InvoiceDisputeMessage.belongsTo(InvoiceDispute, { foreignKey: 'dispute_id', as: 'dispute', onDelete: 'CASCADE' });
  InvoiceDispute.hasMany(InvoiceDisputeMessage, { foreignKey: 'dispute_id', as: 'messages', onDelete: 'CASCADE' });
  InvoiceDisputeMessage.belongsTo(User, { foreignKey: 'sender_user_id', as: 'senderUser', onDelete: 'CASCADE' });
  User.hasMany(InvoiceDisputeMessage, { foreignKey: 'sender_user_id', as: 'disputeMessages', onDelete: 'CASCADE' });
  InvoiceDisputeMessage.belongsTo(Farm, { foreignKey: 'sender_farm_id', as: 'senderFarm', onDelete: 'CASCADE' });
  Farm.hasMany(InvoiceDisputeMessage, { foreignKey: 'sender_farm_id', as: 'sentDisputeMessages', onDelete: 'CASCADE' });

  // Invoice notifications - depends on Invoice, User, and Farm
  InvoiceNotification.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceNotification, { foreignKey: 'invoice_id', as: 'notifications', onDelete: 'CASCADE' });
  InvoiceNotification.belongsTo(User, { foreignKey: 'recipient_user_id', as: 'recipientUser', onDelete: 'CASCADE' });
  User.hasMany(InvoiceNotification, { foreignKey: 'recipient_user_id', as: 'invoiceNotifications', onDelete: 'CASCADE' });
  InvoiceNotification.belongsTo(Farm, { foreignKey: 'recipient_farm_id', as: 'recipientFarm', onDelete: 'CASCADE' });
  Farm.hasMany(InvoiceNotification, { foreignKey: 'recipient_farm_id', as: 'receivedInvoiceNotifications', onDelete: 'CASCADE' });
  InvoiceNotification.belongsTo(Farm, { foreignKey: 'sender_farm_id', as: 'senderFarm', onDelete: 'CASCADE' });
  Farm.hasMany(InvoiceNotification, { foreignKey: 'sender_farm_id', as: 'sentInvoiceNotifications', onDelete: 'CASCADE' });

  // ============================================================================
  // PASSWORD MANAGER SYSTEM
  // ============================================================================

  // Password group associations
  PasswordGroup.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(PasswordGroup, { foreignKey: 'farm_id', as: 'passwordGroups' });
  PasswordGroup.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(PasswordGroup, { foreignKey: 'created_by', as: 'createdPasswordGroups' });

  // Password associations
  Password.belongsTo(PasswordGroup, { foreignKey: 'group_id', as: 'group' });
  PasswordGroup.hasMany(Password, { foreignKey: 'group_id', as: 'passwords' });
  Password.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(Password, { foreignKey: 'created_by', as: 'createdPasswords' });

  // Password group permissions
  PasswordGroupPermission.belongsTo(PasswordGroup, { foreignKey: 'group_id', as: 'group' });
  PasswordGroup.hasMany(PasswordGroupPermission, { foreignKey: 'group_id', as: 'permissions' });
  PasswordGroupPermission.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });
  Role.hasMany(PasswordGroupPermission, { foreignKey: 'role_id', as: 'passwordGroupPermissions' });
  PasswordGroupPermission.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  User.hasMany(PasswordGroupPermission, { foreignKey: 'user_id', as: 'passwordGroupPermissions' });

  // User recovery keys
  UserRecoveryKey.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  User.hasOne(UserRecoveryKey, { foreignKey: 'user_id', as: 'recoveryKey' });


  // ============================================================================
  // BILLING SYSTEM
  // ============================================================================

  // Bill category associations - depends on Farm
  BillCategory.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(BillCategory, { foreignKey: 'farm_id', as: 'billCategories' });

  // Bill associations - depends on Farm, BillCategory, Vendor, and User
  Bill.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(Bill, { foreignKey: 'farm_id', as: 'bills' });
  Bill.belongsTo(BillCategory, { foreignKey: 'category_id', as: 'category' });
  BillCategory.hasMany(Bill, { foreignKey: 'category_id', as: 'bills' });
  Bill.belongsTo(Vendor, { foreignKey: 'vendor_id', as: 'vendor' });
  Vendor.hasMany(Bill, { foreignKey: 'vendor_id', as: 'bills' });
  Bill.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(Bill, { foreignKey: 'created_by', as: 'createdBills' });

  // Bill attachment associations - depends on Bill and User
  BillAttachment.belongsTo(Bill, { foreignKey: 'bill_id', as: 'bill' });
  Bill.hasMany(BillAttachment, { foreignKey: 'bill_id', as: 'attachments' });
  BillAttachment.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader' });
  User.hasMany(BillAttachment, { foreignKey: 'uploaded_by', as: 'uploadedBillAttachments' });

  // Recurring bill associations - depends on Bill
  RecurringBill.belongsTo(Bill, { foreignKey: 'bill_id', as: 'bill', onDelete: 'CASCADE' });
  Bill.hasOne(RecurringBill, { foreignKey: 'bill_id', as: 'recurringSchedule', onDelete: 'CASCADE' });

  // Bill payment associations - depends on Bill and User
  BillPayment.belongsTo(Bill, { foreignKey: 'bill_id', as: 'bill' });
  Bill.hasMany(BillPayment, { foreignKey: 'bill_id', as: 'payments' });
  BillPayment.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(BillPayment, { foreignKey: 'created_by', as: 'createdBillPayments' });

  // Bill transaction associations (linking bills to transactions) - depends on Bill, Transaction, and User
  BillTransaction.belongsTo(Bill, { foreignKey: 'bill_id', as: 'bill' });
  Bill.hasMany(BillTransaction, { foreignKey: 'bill_id', as: 'transactions' });
  BillTransaction.belongsTo(Transaction, { foreignKey: 'transaction_id', as: 'transaction' });
  Transaction.hasMany(BillTransaction, { foreignKey: 'transaction_id', as: 'linkedBills' });
  BillTransaction.belongsTo(User, { foreignKey: 'linked_by', as: 'linkedBy' });
  User.hasMany(BillTransaction, { foreignKey: 'linked_by', as: 'linkedBillTransactions' });

  // Verify that critical associations are properly set up
  verifyAssociations();
};
